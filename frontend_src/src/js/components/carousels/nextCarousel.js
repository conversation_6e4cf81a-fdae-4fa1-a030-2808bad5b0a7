const Site = window.Site || {};

Site.nextCarousel = (function() {
    const nextCarousel = document.querySelector('.next-step__carousel');

    function initialize() {
        const options = {
            contain: true,
            imagesLoaded: true,
            prevNextButtons: false,
            pageDots: false,
            on: {
                ready: () => {
                    window.dispatchEvent(new Event('resize'));
                },
                settle() {
                    window.Site.utils.manageCarouselButtons(nextCarousel);
                },
            },
        };

        const flickity = new window.Flickity('.next-step__carousel', options);

        Site.carouselsUtils.addProgressBar(flickity, '.next-step__carousel');
    }

    return {
        initialize,
    };
}());
