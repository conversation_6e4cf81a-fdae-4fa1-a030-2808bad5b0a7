.single-shelf-option {
    background-color: rgba($ds-offblack-800, 0.28);
    border-radius: $border-radius-xl;
    position: absolute;
    top: 0;
    left: 0;

    &__color {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        box-sizing: border-box;
        border: 2px solid $ds-grey-600;
    }

    &__link {
        transition: color $basic-transition;

        .single-shelf-option__path {
            transition: fill $basic-transition;
        }
    }
    &__link:hover {
        color: $ds-orange;

        .single-shelf-option__path {
            fill: $ds-orange;
        }
    }
}
