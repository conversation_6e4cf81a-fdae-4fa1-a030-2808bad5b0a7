.elevator-pitch {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;

  &--hidden {
    display: none;
  }

  &--plywood {
    .elevator-pitch__reveal-item {
      max-width: 680px;
    }

    .elevator-pitch__reveal-item--2, .elevator-pitch__reveal-item--3 {
      max-width: 480px;
    }
  }

  &__container {
    max-width: 100%;
    overflow: hidden;
    position: relative;
    transition: filter .2s ease;

    &--blur {
      filter: blur(8px)
    }
  }

  &__reveals {
    opacity: 0;
    transition: opacity .2s ease;
  }

  &__reveal-item {
    position: absolute;
    z-index: 1;
    max-width: 580px;
    opacity: 0;

    &--1 {
      top: 20%;
      left: 50%;
      width: 100%;
      transform: translateX(-50%);
    }

    &--2 {
      top: 50%;
      left: 7%;
      text-align: left;
      transform: translateY(-50%);
    }

    &--3 {
      top: 50%;
      left: 53%;
      text-align: left;
      transform: translateY(-50%);
    }
  }

  &__placeholder {
    position: absolute;
    width: 110%;
    height: 110%;
    left: -5%;
    top: -5%;
    filter: blur(32px)
  }

  &__placeholder-img {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }

  &-placeholder {
    &--hidden {
      display: none;
    }
    &__text {
      position: absolute;
      z-index: 1;
      max-width: 580px;
      top: 20%;
      left: 50%;
      width: 100%;
      transform: translateX(-50%);
    }
    &__image {
      display: block;
      width: 100%;
      height: 100vh;
      object-fit: cover;
    }
  }
}

.animation-loader {
  width: 300px;
  height: 2px;
  background: rgba(237, 240, 240, .5);
  position: absolute;
  left: 50%;
  top: 50%;
  z-index: 3;
  transform: translate(-50%, -50%);
}

.watty-animation-loader--position {
  position: relative;
  transform: none;
  top: 0;
  left: 0;
}

.animation-loader-progressbar {
  display: block;
  width: 0;
  height: 2px;
  background: $ds-orange;
  position: relative;
  z-index: 4;
}

.animation-loader-copy {
  display: inline-block;
  position: relative;
  left: 50%;
  top: 10px;
  color: rgba(20, 20, 20, 1);
  transform: translateX(-50%);
  animation: text-blink 1s infinite alternate;
}

@keyframes text-blink {
  0% {
    opacity: 0.2;
  }
  100% {
    opacity: 1;
  }
}

.scroll-pin {
  transform: translate3d(0, 0, 0);
}

.pdp-landing {
  .scrollmagic-pin-spacer {
    height: 100vh;
    min-height: 100vh;
    position: relative;
    box-sizing: content-box;
    top: auto;
    left: auto;
    bottom: auto;
    right: auto;
    margin: 0;
    display: block;
    background: #a5978a !important;

    .elevator-pitch {
      background: #a5978a !important;
      position: relative;
      box-sizing: content-box;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      margin: 0;
      display: block;
    }
  }
}
