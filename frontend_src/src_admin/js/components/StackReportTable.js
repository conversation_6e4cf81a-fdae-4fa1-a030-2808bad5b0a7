import React from 'react';

//import plants from '../data/plants';

import <PERSON><PERSON>r<PERSON>og from './ErrorLog'

const TYPE_USAGE = 1;
const TYPE_SHIPMENT = 2;
const TYPE_TRANSPORT = 3;
const TYPE_ADJUSTMENT = 4;
const TYPE_REPORT = 5;

class StackReportTable extends React.Component{
    constructor(props) {
        super(props);
        this.state = {
          change_factor: 1,
          change_batch_factor_min: null,
          change_batch_factor_max: null,
            date_overrides: {}
        };
        this.handleSelectChange = this.handleSelectChange.bind(this);
        this.handleShipmentChange = this.handleShipmentChange.bind(this);
    }
    handleSelectChange() {
        this.setState({
            change_factor: parseFloat(this.refs.change_factor.value),
            change_batch_factor_min: this.refs.change_batch_factor_min.value ? parseInt(this.refs.change_batch_factor_min.value) : null,
            change_batch_factor_max: this.refs.change_batch_factor_max.value ? parseInt(this.refs.change_batch_factor_max.value) : null,
        })
    }
    handleShipmentChange(event) {
        let item_to_change = [parseInt(event.target.id.replace('ship','')), event.target.value];
        let tmp = Object.assign({}, this.state.date_overrides);
        tmp[item_to_change[0]] = item_to_change[1];
        this.setState(
            {date_overrides : tmp}
        );
    }
    render(){
        let event_lines = [];
        let stats = {
            'total_entries': 0,
            'amount_entries': 0,
            'total_usage': 0,
            'amount_usage': 0,
            'total_shipments': 0,
            'amount_shipments': 0,
            'total_phantoms': 0,
            'amount_phantoms': 0,
        };
        let shipment_list = this.props.stack_events.events.filter(x=> x['entry_type'] == TYPE_SHIPMENT);
        this.props.stack_events.events.forEach((event, i) =>
        {
            if (event['entry_type'] == TYPE_REPORT ) {
                event_lines.push(<div key={i} style={{color: 'red'}}>{event.our_amount } / { event.event } / Diff: { event.our_amount_diff } / Batch: {event.batch }<br/><br/></div>)
            } else{
                if (event['entry_type'] == TYPE_SHIPMENT){
                    stats.total_entries += 1;
                    stats.amount_entries += event.amount;
                    stats.total_shipments += 1;
                    stats.amount_shipments += event.amount;
                    event_lines.push(<div key={i} style={{color: "green"}}>{ event.amount } / { event.date } / { event.event } / Batch: { event.batch }<br/><br/></div>)
                } else if (event.phantom){
                    stats.total_entries += 1;
                    stats.amount_entries += event.amount;
                    stats.total_phantoms += 1;
                    stats.amount_phantoms += event.amount;
                    event_lines.push(<div key={i} style={{color: "blue"}}>{ event.amount } / { event.date } / { event.event } / Batch: { event.batch }<br/><br/></div>)
                } else {
                    let tmp_value = event.amount;
                    if (!event.batch){
                        // no batch
                    } else if (!this.state.change_batch_factor_min || !(this.state.change_batch_factor_min <= parseInt(event.batch))){
                        // nothing
                    } else if (!this.state.change_batch_factor_max || !(this.state.change_batch_factor_max >= parseInt(event.batch))){
                        // nothing again
                    } else{
                        tmp_value *= this.state.change_factor;
                    }
                    stats.total_entries += 1;
                    stats.amount_entries += tmp_value;
                    stats.total_usage += 1;
                    stats.amount_usage += tmp_value;

                    event_lines.push(<div dangerouslySetInnerHTML={{__html: `${tmp_value} / ${event.date}/ ${event.event} / Batch: ${event.batch}<br/><br/>` }} key={i}></div>)
                }
            }
        });
        return (
            <div id="errorLogs">
            <h2>History for this stack</h2>
                <table>
                    <tbody>
                    <tr>
                        <td colSpan="5">
                            Change factor (1 == without change):
                            <input ref="change_factor" defaultValue="1" />
                            Batch range filter for change factor:
                            <input ref="change_batch_factor_min" defaultValue="" />
                            <input ref="change_batch_factor_max" defaultValue="" />
                            <input type="button" onClick={this.handleSelectChange} value='Recalculate'/>
                        </td>
                    </tr>
                    <tr>
                        <td>Type</td><td>Total entries</td><td>Usages</td><td>Shipments</td><td>Phantoms</td>
                    </tr>
                    <tr>
                            <td>Number</td><td>{stats.total_entries}</td><td>{stats.total_usage}</td><td>{stats.total_shipments}</td><td>{stats.total_phantoms}</td>
                    </tr><tr>
                            <td>Amount</td><td>{stats.amount_entries.toFixed(2)}</td><td>{stats.amount_usage.toFixed(2)}</td><td>{stats.amount_shipments.toFixed(2)}</td><td>{stats.amount_phantoms.toFixed(2)}</td>
                    </tr>
                    </tbody>
                </table>
                <table>
                    <tbody>
                    <tr>
                        <td>Shipment id</td><td>Amount</td><td>Old Date</td><td>New Date</td>
                    </tr>
                        {shipment_list.map((shipment, i) => {
                            return (<tr key={i}>
                                <td>{shipment.shipment}</td>
                                <td>{shipment.amount}</td>
                                <td>{new Date(shipment.date).toISOString().slice(0, 10)}</td>
                                <td>
                                    <div className="col-xs-4">
                                        <input className="form-control" id={"ship"+shipment.shipment} type="date" defaultValue={new Date(shipment.date).toISOString().slice(0, 10)}
                                               ref={"shipment_edit_" + i} onChange={this.handleShipmentChange}/>
                                    </div>
                                </td>
                            </tr>)
                        })}
                    </tbody>
                </table>
                <table>
                    <thead>
                        <tr>
                            <th>Amount</th>
                            <th>Diffrence</th>
                            <th>Diffrence cost</th>
                            <th>Description</th>
                            <th>Description per batch</th>
                        </tr>
                    </thead>
                    <tbody>

                    <tr>
                        <td>
                            { this.props.stack_events.amount }
                        </td>
                        <td>
                            { this.props.diffrence }
                        </td>
                        <td>
                            { this.props.diffrence_cost }
                        </td>
                        <td>
                            <span style={{fontSize: '10px'}}>
                                {event_lines}
                            </span>
                        </td>
                        <td>
                            <span style={{fontSize: '10px'}}>
                                {this.props.batch_and_usages.map((event,i) => {
                                    return (<div key={i}> { event[1] } / { event[2] } / Batch: { event[0] }<br/><br/></div>)
                                })}
                            </span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            )
    }
};
export default StackReportTable;