<template>
  <div class="configurator-material-toggle">
    <div v-if="currentOption === false">
      <slot name="left" />
    </div>
    <div v-else>
      <slot name="right" />
    </div>
    <div class="configurator-material-toggle-header row">
      <div class="col-xs-12">
        <ToggleGroup
          v-model="option"
          noUppercase
          :options="toggleOptions"
          :trigger="trigger"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ToggleGroup from '@componentsConfigurator/TylkoToggleGroup';

export default {
  name: 'ConfiguratorMaterialToggle',
  components: {
    ToggleGroup,
  },
  props: {
    options: {
      type: Array,
      required: true,
    },
    isShelfToggle: {
      type: Boolean,
      required: true,
      default: false,
    },
    defaultOption: {
      type: Boolean,
      required: true,
      default: false,
    },
    trigger: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      currentOption: this.defaultOption,
    };
  },
  computed: {
    toggleOptions() {
      return this.options;
    },
    option: {
      get() {
        return this.currentOption;
      },
      set(value) {
        this.currentOption = value;
      },
    },
  },
  watch: {
    defaultOption() {
      this.currentOption = this.defaultOption;
    },
    option(newValue) {
      if (this.isShelfToggle) {
        const { shelfType } = this.toggleOptions.find(option => option.value === newValue);
        this.$store.dispatch('SET_NEW_SHELF_TYPE', shelfType);
      }
    },
  },
};
</script>

<style lang="scss">
@import '../../../../src/scss/utils/variables';
@import '../../../../src/scss/utils/mixins';

.configurator-material-toggle-header {
  align-items: center;

  @include mobile {
    .toggle-button {
      height: 24px;
    }

    .overlay {
      height: 28px;
    }
  }
}
</style>
