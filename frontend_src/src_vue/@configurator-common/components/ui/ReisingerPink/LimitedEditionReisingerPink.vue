<template>
  <a
    class="limted-edition-resinger-container"
    :class="{ mobile: $root.isMobile }"
    :href="getLandingPageLink"
    target="_blank"
  >
    <img
      class="limted-edition-resinger"
      svg-inline
      src="@tylko_ui/static-assets/limited-edition-reisnger-pink.svg"
    >
  </a>
</template>
<script>
import { LANDING_PAGE_URL } from './reisingerPink.consts';

export default {
  computed: {
    getLandingPageLink() {
      return this.getLocalizedUrl({
        enUrl: LANDING_PAGE_URL,
        frUrl: LANDING_PAGE_URL,
        deUrl: LANDING_PAGE_URL,
        nlUrl: LANDING_PAGE_URL,
        esUrl: LANDING_PAGE_URL,
        plUrl: LANDING_PAGE_URL,
        svUrl: LANDING_PAGE_URL,
        daUrl: LANDING_PAGE_URL,
        itUrl: LANDING_PAGE_URL,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.limted-edition-resinger-container {
  position: absolute;
  top: 24px;
  left: 24px;

  .limted-edition-resinger {
    height: 100%;
    max-width: 150px;
    transition: background-color .3s ease-in-out;
    &:hover {
      background: #fff;
      g {
        fill: #e4d7e0;
      }
    }
  }

  &.mobile {
    left: 16px;
    top: 65px;

    .limted-edition-resinger {
      max-width: 75px;
    }
  }
}
</style>
;
