import {
  initTheRenderer, updateShelfGeometry, renderer, FPM,
} from '../../../core';

export default {
  async INIT_RENDERER({ dispatch, rootState }, { configurator, canvasWidth, canvasHeight }) {
    const { configuration: { material }, shelfType } = rootState;
    const geom = FPM.getGeometry();

    const args = {
      container: configurator,
      canvasWidth,
      canvasHeight,
      shelfType,
      shelfColor: material,
      geom,
      cameraType: 'tylkoCamera',
      dispatcher: this.dispatch,
      store: rootState,
      material,
      category: rootState.category,
    };

    await initTheRenderer(args);
    dispatch('UPDATE_HAS_ANY_ITEMS');
  },
  UPDATE_RENDERER({ dispatch, rootState: { FPM: { geom }, ui } }, { geom: renderGeom, updateCamera }) {
    const newGeom = FPM.getGeometry();

    if (renderer.isReady) {
      updateShelfGeometry(newGeom, updateCamera);
      setTimeout(() => {
        dispatch('FPM/UPDATE_PRODUCT_DETAILS', null, { root: true });
      }, 0);
      dispatch('UPDATE_HAS_ANY_ITEMS');
    }
  },
  UPDATE_ITEMS_VISIBILITY({ commit, dispatch }, payload) {
    commit('updateItemsVisibilityState', payload);
    if (!renderer.isReady) return;
    renderer.toggleItemsVisibility(payload);
  },
  UPDATE_MATERIAL({ rootState }, material) {
  },
  UPDATE_ROW_HEIGHT({ rootState }) {
  },
  UPDATE_OFFSCREEN_RENDERER({ rootState }) {
  },
  UPDATE_HAS_ANY_ITEMS({ dispatch }) {
    const hasItems = renderer.isReady && renderer.anyItemsOnShelves();
    dispatch('commonItemsOnShelves/UPDATE_HAS_ITEMS_ON_SHELVES', hasItems, { root: true });
  },
};
