<template>
  <transition name="fade">
    <div
      v-show="showSnackbar"
      class="additional-sections-snackbar dimensions-snackbar"
    >
      <div
        class="bg-white tylko-snackbar normal-14 text-offblack-600 text-center py-8 pl-16 pr-48 mx-16 lg:mx-0 z-20"
      >
        <p>
          {{ $t('cvert_measuring_guide_snackbar_body') }}
          <span
            class="tylko-link-with-cheveron tylko-link-with-cheveron--orange inline cursor-pointer bold-14 text-orange link__text text-lowercase"
            @click="openMeasuringGuide()"
          >    {{ $t('cvert_measuring_guide_snackbar_button') }}
            <svg
              class="tylko-link-chevron"
              width="8"
              height="8"
              viewBox="0 0 8 8"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2.40002 4.00626L2.07036 3.63034C1.96195 3.7254 1.89986 3.86263 1.90002 4.00681C1.90018 4.151 1.96258 4.28809 2.07119 4.38291L2.40002 4.00626ZM5.27036 0.824073L2.07036 3.63034L2.72969 4.38218L5.92969 1.57592L5.27036 0.824073ZM2.07119 4.38291L5.27119 7.17665L5.92886 6.42334L2.72886 3.62961L2.07119 4.38291Z"
              />
            </svg><span /></span>
        </p>
        <button
          class="tylko-snackbar__close p-12"
          @click="closeSnackbar"
        />
      </div>
    </div>
  </transition>
</template>
<style lang="scss" scoped>
.dimensions-snackbar {
  .tylko-snackbar {
    width: max-content;
  }
}
</style>

<script>
export default {
  data() {
    return {
      showSnackbar: false,
    };
  },
  computed: {
    displaySnackbar() {
      return this.$store.getters['dimensions/GET_SHOW_DIMENSIONS'];
    },
  },
  created() {
    this.snackbarWatcher = this.$store.watch(
      () => this.$store.getters['dimensions/GET_SHOW_DIMENSIONS'],
      () => {
        this.showSnackbar = !!this.displaySnackbar;
      },
    );
  },
  beforeDestroy() {
    this.snackbarWatcher();
  },

  methods: {
    closeSnackbar() {
      this.showSnackbar = false;
    },
    openMeasuringGuide() {
      this.$store.dispatch('renderer/RENDERER_SET_COMPONENT_VIEW');
      PubSub.publish('ecommerceServiceOpenMeasuringGuideModal');
    },
  },
};
</script>
