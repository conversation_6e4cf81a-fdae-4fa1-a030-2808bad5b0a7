<template>
  <div class="layout-ac">
    <div class="flex-wrapper">
      <thumbnails>
        <template v-slot="{ thumb, activeComponent }">
          <Miniature
            :id="thumb.id"
            ref="miniature"
            :geom="thumb.format"
            :active="thumb.series_id === activeComponent.series_id"
            :createMiniatureSize="createSize(thumb.format)"
          />
        </template>
      </thumbnails>
    </div>
  </div>
</template>

<script>

  import Thumbnails from '@configurator-common/components/ui/Thumbnails';
  import Miniature from '@configurator-common/components/ui/Miniature';
  import { componentsCopy } from '../../mixins/utils';

  export default {
    components: {
      Thumbnails,
      Miniature,
    },
    mixins: [componentsCopy],
    methods: {
      createSize(data) {
        const wideComponent = data.components[0].width > 720;
        const highComponent = data.components[0].height > 2100;
        const width = wideComponent ? 58 : 48;
        const height = highComponent ? 115 : 109;
        return { width, height };
      },
    },
  };
</script>
