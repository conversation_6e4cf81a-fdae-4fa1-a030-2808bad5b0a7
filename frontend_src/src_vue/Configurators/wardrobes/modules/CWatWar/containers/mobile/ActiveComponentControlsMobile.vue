<template>
  <div class="mobile-active-component">
    <transition name="fade">
      <div
        v-show="loaded"
        ref="header"
        class="configurator-header px-16"
      >
        <button
          class="back-button"
          @click="deactivateComponent"
        >
          <img
            svg-inline
            src="@tylko_ui/icons-cplus/ic_chevron_left.svg"
            alt="whole view"
            class="mr-8"
          >
          <span class="bold-14 text-grey-900">{{
            $t("cwatwar_mobile_exterior")
          }}</span>
        </button>
        <PriceWithDiscount
          v-if="!$options.isNil(material) && furnitureCategory"
          isInterior
          v-bind="{ shelfType, furnitureCategory }"
          :materialConfig="$options.materialConfig"
          :activeMaterial="material"
          furnitureType="watty"
        />
      </div>
    </transition>
    <div
      ref="modal"
      class="configurator-modal safari_only"
    >
      <div class="active-component-canvas-wrapper">
        <div
          ref="modalConfiguratorWrapper"
          style="display: flex"
        />
        <ComponentIndicators />
        <CommonIndicators />
      </div>
      <transition name="fade">
        <div
          v-show="loaded"
          class="active-component-content-wrapper pt-8"
        >
          <tylkoTabs
            v-if="tabs.length"
            ref="tabs"
            name="active-component-tylko-tabs"
            :activeTab="tab"
            class="pb-16"
          >
            <transition-group
              name="additional-tab"
              tag="p"
            >
              <button
                v-for="(t, i) in tabs"
                :key="t.label"
                class="c-tab-button additional-tab-item"
                :class="[
                  { active: tab === i },
                  { doors: tabs[i].code === 'door_direction' }
                ]"
                @click="
                  e => {
                    tab = i;
                  }
                "
              >
                {{ t.label }}
              </button>
            </transition-group>
          </tylkoTabs>
          <tylkoContainers
            v-if="tabs.length"
            :selected="tabs[tab].code"
            class="configuration-wrapper"
          >
            <tylkoContainer
              name="layout"
              wrapperClass="flex-column"
            >
              <Layout />
            </tylkoContainer>
            <tylkoContainer name="column_width">
              <ColumnWidth />
            </tylkoContainer>
            <tylkoContainer
              v-show="doorDirection && doorDirection.visible"
              name="door_direction"
              wrapperClass="flex-column middle-xs px-16"
            >
              <DoorDirection />
            </tylkoContainer>
          </tylkoContainers>
          <ProStorageTips />
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { isNil } from 'lodash';

import materialConfig from '@configurator-common/helpers/materialsConfigWatty';
import PriceWithDiscount from '@configurator-common/components/PriceWithDiscount';
import {
  TylkoContainer,
  TylkoContainers,
  TylkoTabs,
} from '@componentsConfigurator';
import CommonIndicators from './canvasIndicators/CommonIndicators';
import ComponentIndicators from './canvasIndicators/ComponentIndicators';
import { updateConfigurator } from '../../mixins/storeComponentCommunication';
import Layout from './activeComponentContent/Layout';
import ColumnWidth from './activeComponentContent/ColumnWidth';
import ProStorageTips from './activeComponentContent/ProStorageTips';
import DoorDirection from './activeComponentContent/DoorDirection';

export default {
  materialConfig: materialConfig(),
  components: {
    CommonIndicators,
    TylkoContainer,
    TylkoContainers,
    TylkoTabs,
    Layout,
    ColumnWidth,
    ComponentIndicators,
    PriceWithDiscount,
    ProStorageTips,
    DoorDirection,
  },
  mixins: [updateConfigurator],
  data() {
    return {
      loaded: false,
      flipDoorsPlaceholder: {
        label: this.$t('cwatwar_doors'),
        code: 'door_direction',
      },
      activeTabWatcher: null,
    };
  },
  computed: {
    tabs: {
      get() {
        return this.$store.getters['ui/GET_ACTIVE_COMPONENT_TABS'];
      },
      set(tabs) {
        this.$store.dispatch('ui/UPDATE_ACTIVE_COMPONENT_TABS', tabs);
      },
    },
    tab: {
      get() {
        return this.$store.getters['ui/GET_ACTIVE_COMPONENT_ACTIVE_TAB'];
      },
      set(newValue) {
        this.$store.dispatch('ui/UPDATE_ACTIVE_COMPONENT_ACTIVE_TAB', newValue);
      },
    },
    doorDirection: {
      get() {
        return this.$store.getters['ui/GET_ACTIVE_COMPONENT_PARAMS']
          .doorDirection;
      },
    },
    shelfType() {
      return this.$store.getters.GET_SHELF_TYPE;
    },
    material() {
      return this.$store.getters['renderer/GET_MATERIAL'];
    },
    furnitureCategory() {
      const { category } = this.$store.getters['commonFurniture/GET_ITEM'];
      return category;
    },
    isInternalDrawerStyle() {
      return (
        this.$store.getters['configuration/GET_DRAWER_STYLE'] === 'internal'
      );
    },
  },
  beforeDestroy() {
    this.activeTabWatcher();
    this.$parent.$refs.configuratorWrapper.appendChild(
      this.$refs.modalConfiguratorWrapper.firstChild,
    );
    this.loaded = false;
    this.tab = 0;
  },
  mounted() {
    const configuratorWrapper = this.$refs.modalConfiguratorWrapper;
    const { configurator } = this.$parent.$refs;
    configuratorWrapper.appendChild(configurator);
    this.tabs = [
      {
        label: this.$t('cwatwar_mobile_layout'),
        code: 'layout',
      },
      {
        label: this.$t('cwatwar_col_width'),
        code: 'column_width',
      },
    ];
    if (this.doorDirection && this.doorDirection.visible) {
      this.tabs = [...this.tabs, Object.assign({}, this.flipDoorsPlaceholder)];
    }
    this.loaded = true;
  },
  created() {
    this.activeTabWatcher = this.$store.watch(
      (state, getters) => getters.GET_ACTIVE_COMPONENT,
      () => {
        if (this.doorDirection.visible) {
          if (!this.tabs.some(tab => tab.code === 'door_direction')) {
            this.tabs = [
              ...this.tabs,
              Object.assign({}, this.flipDoorsPlaceholder),
            ];
          }
        } else {
          if (this.tabs[this.tab].code === 'door_direction') this.tab = 0;
          this.tabs = this.tabs.filter(tab => tab.code !== 'door_direction');
        }
      },
    );
  },
  methods: {
    deactivateComponent() {
      this.$store.dispatch('DEACTIVATE_COMPONENT');
      this.$store.dispatch('renderer/RESET_TAB_SPECIFIC_VIEW');
    },
  },
  isNil,
};
</script>
