export default {
    SET_IS_SLIDER_MOVING({ commit }, payload) {
        commit('UPDATE_IS_SLIDER_MOVING', payload);
    },
    SET_IS_ORBIT_MOVING({ commit, dispatch, rootState: { renderer } }, payload) {
        commit('UPDATE_IS_ORBIT_MOVING', payload);
        if (renderer.isDoorsOpen && payload) {
            dispatch('renderer/UPDATE_DOORS_OPEN', true, { root: true });
        }
    },
    SET_IS_CAMERA_ANIMATING({ commit }, payload) {
        commit('UPDATE_IS_CAMERA_ANIMATING', payload);
    },
};
