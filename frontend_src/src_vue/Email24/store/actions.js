import router from '../router';
import { findPage, snakeToCamel } from '../utils/helpers';
import { EMAIL_24_STATUSES } from '../utils/consts';

export async function FETCH_DATA({ dispatch, commit }, { lat }) {
  try {
    const response = await dispatch('api/REQUEST', {
      method: 'GET',
      url: `${window.LOGISTIC_URL}/internal-api/v1/email24/open?lat=${lat}`,
      headers: { Authorization: `Token ${window.USER_TOKEN}` },
      data: {},
    }, { root: true });
    const {
      id,
      address,
      status,
      order,
      productDetails,
      isEstimatedDeliveryDate,
      estimatedDateOfPickupByCourier,
    } = snakeToCamel(response.data);

    commit('SET_DELIVERY_ADDRESS', address);
    commit('SET_ID', id);
    commit('SET_ORDER_ID', order);
    commit('SET_PRODUCT_DETAILS', productDetails);
    commit('SET_IS_ESTIMATED_DELIVERY_DATE', isEstimatedDeliveryDate);
    commit('SET_ESTIMATED_DATE_OF_PICKUP_BY_COURIER', estimatedDateOfPickupByCourier);

    if (status === EMAIL_24_STATUSES.CONFIRMED) {
      const {
        note,
        floor,
        hasElevator,
        dateConfirmed,
      } = snakeToCamel(response.data);
      commit('SET_NOTE', note);
      commit('SET_FLOOR', floor);
      commit('SET_HAS_ELEVATOR', hasElevator);
      commit('SET_DATE_CONFIRMED', dateConfirmed);
    }

    const currentPage = findPage(status);
    router.push({ name: currentPage, query: { lat } }).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        throw err;
      }
    });
  } catch (error) {
    Site.Notify.onError(this.$root.copy.error_connection_1, 4000);
    throw error;
  }
}

export async function SAVE_DATA({ dispatch, state }) {
  // eslint-disable-next-line no-console
  console.log('save!');
  try {
    await dispatch('api/REQUEST', {
      method: 'PATCH',
      url: `${window.LOGISTIC_URL}/internal-api/v1/email24/${state.id}/confirm`,
      headers: { Authorization: `Token ${window.USER_TOKEN}` },
      data: {
        floor: state.hasElevator ? null : state.floor,
        has_elevator: state.hasElevator,
        note: state.note,
        delivery_info: state.deliveryInfo,
        request_user_id: window.request_user_id,
      },
    }, { root: true });
  } catch (error) {
    throw error;
  } finally {
    // eslint-disable-next-line no-restricted-globals
    location.reload();
  }
}

export function SET_ID({ commit }, id) {
  commit('SET_ID', id);
}

export function SET_ORDER_ID({ commit }, orderId) {
  commit('SET_ORDER_ID', orderId);
}

export function SET_PRODUCT_DETAILS({ commit }, productDetails) {
  commit('SET_PRODUCT_DETAILS', productDetails);
}

export function SET_IS_ESTIMATED_DELIVERY_DATE({ commit }, estimatedDeliveryDate) {
  commit('SET_IS_ESTIMATED_DELIVERY_DATE', estimatedDeliveryDate);
}

export function SET_ESTIMATED_DATE_OF_PICKUP_BY_COURIER({ commit }, estimatedDateOfPickupByCourier) {
  commit('SET_ESTIMATED_DATE_OF_PICKUP_BY_COURIER', estimatedDateOfPickupByCourier);
}

export function SET_NOTE({ commit }, note) {
  commit('SET_NOTE', note);
}

export function SET_DELIVERY_INFO({ commit }, deliveryInfo) {
  commit('SET_DELIVERY_INFO', deliveryInfo);
}

export function SET_FLOOR({ commit }, floor) {
  commit('SET_FLOOR', floor);
}

export function SET_HAS_ELEVATOR({ commit }, hasElevator) {
  commit('SET_HAS_ELEVATOR', hasElevator);
}

export function SET_DELIVERY_ADDRESS({ commit }, deliveryAddress) {
  commit('SET_DELIVERY_ADDRESS', deliveryAddress);
}

export function SET_DATE_CONFIRMED({ commit }, dateConfirmed) {
  commit('SET_DATE_CONFIRMED', dateConfirmed);
}
