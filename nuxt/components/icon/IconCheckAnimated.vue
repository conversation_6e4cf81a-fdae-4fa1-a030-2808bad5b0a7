<template>
  <svg
    class="icon-check mx-auto mb-16 md:mb-24"
    width="66"
    height="66"
    viewBox="0 0 66 66"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M65 33C65 50.6731 50.6731 65 33 65C15.3269 65 1 50.6731 1 33C1 15.3269 15.3269 1 33 1C50.6731 1 65 15.3269 65 33Z"
      stroke="#3CBE5A"
      stroke-width="1.5"
    />
    <path
      d="M19 31.5L29 41.5L46.5 24"
      stroke="#3CBE5A"
      stroke-width="1.5"
      stroke-linecap="round"
    />
  </svg>
</template>

<style lang="scss" scoped>
.icon-check {
  path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 0;
  }

  &.animation {
    path {
      animation: draw 3000ms ease-in-out 4ms forwards;
    }
  }
}

@keyframes draw {
  from {
    stroke-dashoffset: 1000;
  }

  to {
    stroke-dashoffset: 0;
  }
}
</style>
