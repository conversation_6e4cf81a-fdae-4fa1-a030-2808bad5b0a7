<template>
  <ClientOnly>
    <BaseModal v-bind="{ name: 'ModalOrderStatusCheckerFaq', 'content-class': 'w-full max-w-[880px] py-48' }">
      <template #default>
        <div class="m-16 lg:m-32 py-32">
          <h2
            class="bold-28 mb-24 lg:mb-32 text-offblack-800"
            v-html="$t('footer.faq')"
          />
          <ContactFormOrderStatusFaqAccordeon
            v-for="(data, index) in faqData"
            v-bind:key="index"
            class="lg:mb-64"
            v-bind="{
              data: data,
              uniqueId: `order-status-checker-faq-${index}`,
              dataTestid: `order-status-checker-faq-${index}`
            }"
          >
            <template #title>
              {{ $t(`contact.forms.order_status.faq_title${index + 1}`) }}
            </template>
          </ContactFormOrderStatusFaqAccordeon>
        </div>
      </template>
    </BaseModal>
  </ClientOnly>
</template>

<script lang="ts">
import { defineComponent, useContext } from '@nuxtjs/composition-api';

export default defineComponent({

  setup() {
    const { i18n } = useContext();

    const faqData = [
      [
        {
          question: i18n.t('contact.forms.order_status.faq_q1'),
          answer: i18n.t('contact.forms.order_status.faq_a1')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q2'),
          answer: i18n.t('contact.forms.order_status.faq_a2')
        }, {
          question: i18n.t('contact.forms.order_status.faq_q3'),
          answer: i18n.t('contact.forms.order_status.faq_a3')
        }, {
          question: i18n.t('contact.forms.order_status.faq_q4'),
          answer: i18n.t('contact.forms.order_status.faq_a4')
        }
      ],
      [
        {
          question: i18n.t('contact.forms.order_status.faq_q5'),
          answer: i18n.t('contact.forms.order_status.faq_a5')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q6'),
          answer: i18n.t('contact.forms.order_status.faq_a6')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q7'),
          answer: i18n.t('contact.forms.order_status.faq_a7')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q8'),
          answer: i18n.t('contact.forms.order_status.faq_a8')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q9'),
          answer: i18n.t('contact.forms.order_status.faq_a9')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q10'),
          answer: i18n.t('contact.forms.order_status.faq_a10')
        }
      ],
      [
        {
          question: i18n.t('contact.forms.order_status.faq_q11'),
          answer: i18n.t('contact.forms.order_status.faq_a11')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q12'),
          answer: i18n.t('contact.forms.order_status.faq_a12')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q13'),
          answer: i18n.t('contact.forms.order_status.faq_a13')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q14'),
          answer: i18n.t('contact.forms.order_status.faq_a14')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q15'),
          answer: i18n.t('contact.forms.order_status.faq_a15')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q16'),
          answer: i18n.t('contact.forms.order_status.faq_a16')
        }
      ],
      [
        {
          question: i18n.t('contact.forms.order_status.faq_q17'),
          answer: i18n.t('contact.forms.order_status.faq_a17')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q17'),
          answer: i18n.t('contact.forms.order_status.faq_a17')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q18'),
          answer: i18n.t('contact.forms.order_status.faq_a18')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q19'),
          answer: i18n.t('contact.forms.order_status.faq_a19')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q20'),
          answer: i18n.t('contact.forms.order_status.faq_a20')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q21'),
          answer: i18n.t('contact.forms.order_status.faq_a21')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q22'),
          answer: i18n.t('contact.forms.order_status.faq_a22')
        },
        {
          question: i18n.t('contact.forms.order_status.faq_q23'),
          answer: i18n.t('contact.forms.order_status.faq_a23')
        }
      ]
    ];

    return {
      faqData
    };
  }
});
</script>
