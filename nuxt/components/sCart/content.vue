<template>
  <div class="flex flex-col h-full">
    <SCartHeader />
    <SCartEmpty v-if="!cart.cartItemsCount" />
    <template v-else>
      <BaseNotification v-bind="{ 'is-cart': true }" />
      <transition-group
        name="slide-in-out-group"
        tag="ul"
        class="overflow-x-hidden px-16 flex-grow"
      >
        <SCartItem
          v-for="item in cart.cartItems"
          v-bind:key="item.itemId"
          v-bind="{ item }"
        />
      </transition-group>
      <SCartSummary />
    </template>
  </div>
</template>

<script>
import { computed, defineComponent, useStore } from '@nuxtjs/composition-api';

export default defineComponent({
  setup() {
    const store = useStore();

    const cart = computed(() => store.getters['cart/ALL']);

    return {
      cart
    };
  }
});
</script>
