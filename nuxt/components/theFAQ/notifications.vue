<template>
  <div
    ref="notificationsContainer"
    class="overflow-hidden relative border-gray"
  >
    <template v-for="notification in activeNotifications">
      <TheFAQNotificationItem
        ref="notificationItems"
        v-bind:key="`notification${notification.slug}`"
        v-bind="{
          id: notification.slug,
          title: notification.title,
          content: notification.shortAnswer,
          readMoreRoute: FAQPages.getArticleRoute(onGoingIssuesCategorySlug, notification.slug) ,
          isOnlyOneNotificationVisible: activeNotifications.length === 1
        }"
        v-on="{
          removeNotification
        }"
      />
    </template>
  </div>
</template>

<script>

import { defineComponent, nextTick, onMounted, ref, computed } from '@nuxtjs/composition-api';
import { gsap } from 'gsap';
import { useStorage } from '@vueuse/core';
import { FAQPages } from '@/utils/pages';

export default defineComponent({
  props: {
    onGoingIssuesCategorySlug: {
      type: String,
      default: ''
    },
    notifications: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const notificationsContainer = ref(null);
    const notificationItems = ref(null);

    const notificationsLocalStorage = useStorage('FAQ_notifications_closed', {});

    const activeNotifications = computed(() => props.notifications.filter(el => !notificationsLocalStorage.value[el.slug]));

    onMounted(() => {
      activeNotifications.value.length && nextTick(() => {
        gsap.timeline()
          .from(notificationsContainer.value, { height: 0, duration: 0.75, ease: 'power3.out', onComplete: () => notificationsContainer.value.style.height = 'auto' }, 'start')
          .from(notificationItems.value.map(el => el.$el), { opacity: 0, duration: 0.65, y: '+=25%', ease: 'power3.out', stagger: 0.25 }, 'start+=0.5');
      });
    });

    const removeNotification = (slug) => {
      const obj = { ...notificationsLocalStorage.value };

      obj[slug] = true;

      notificationsLocalStorage.value = obj;
    };

    return {
      FAQPages,
      notificationsLocalStorage,
      notificationsContainer,
      notificationItems,
      activeNotifications,
      removeNotification
    };
  }
});
</script>
