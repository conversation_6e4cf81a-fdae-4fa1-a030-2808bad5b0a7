<template>
  <div
    class="overflow-hidden"
    v-bind:class="scrollWrapperClasses"
  >
    <div
      ref="scrollWrapper"
      v-bind:class="scrollAreaClasses"
      class="overflow-x-scroll overflow-y-hidden pb-16 scroll-smooth"
      data-testid="toggles-area"
      style="scroll-behavior: smooth;"
    >
      <BaseToggle
        ref="baseToggle"
        v-bind="$props"
        v-on:input="(val) => $emit('input', val)"
        v-on:isReady="readyHandler"
        v-on:activeIndex="handleScroll"
      />
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, nextTick } from '@nuxtjs/composition-api';

export default defineComponent({
  props: {
    options: {
      type: Array,
      required: true
    },
    value: {
      type: [Number, String],
      required: true
    },
    toggleButtonClasses: {
      type: [String, Array],
      default: ''
    },
    toggleActiveButtonClasses: {
      type: String,
      default: ''
    },
    toggleWrapperClasses: {
      type: [String, Array],
      default: ''
    },
    scrollWrapperClasses: {
      type: String,
      default: ''
    },
    scrollAreaClasses: {
      type: String,
      default: ''
    },
    iconClasses: {
      type: String,
      default: ''
    },
    overlayClasses: {
      type: [String, Array],
      default: ''
    },
    overlayOffset: {
      type: Number,
      default: 0
    },
    variant: {
      type: String,
      default: 'custom'
    }
  },
  setup(_, { emit }) {
    const baseToggle = ref(null);
    const buttons = ref([]);
    const wrapperWidth = ref(null);
    const scrollWrapper = ref(null);

    const handleScroll = (activeIndex) => {
      const parentWidth = scrollWrapper.value.offsetWidth;
      const { offsetWidth, offsetLeft } = baseToggle.value.toggleButtons[activeIndex].$el;
      const wrapperPaddingLeft = parseInt(window.getComputedStyle(scrollWrapper.value).getPropertyValue('padding-left'), 10);
      scrollWrapper.value.scrollLeft = offsetLeft + wrapperPaddingLeft + (offsetWidth / 2) - (parentWidth / 2);
    };

    const readyHandler = (activeIndex) => {
      nextTick(() => {
        const { toggleButtons, $el } = baseToggle.value;
        buttons.value = toggleButtons;
        wrapperWidth.value = $el.offsetWidth;
        handleScroll(activeIndex);
        emit('isReady');
      });
    };

    return { baseToggle, readyHandler, handleScroll, scrollWrapper };
  }
});
</script>
