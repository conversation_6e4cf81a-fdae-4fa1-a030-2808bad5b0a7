import { reactive, ref, useContext, toRaw, useStore } from '@nuxtjs/composition-api';
import Cookies from 'universal-cookie';
import { COOKIES } from '~/utils/api';

const tyCookieExist = ref(false);
const visitorGuid = ref('');

interface CookiesConsent {
  necessary: boolean,
  performance: boolean,
  functional: boolean,
  advertising: boolean,
}

const cookiesConsentState = reactive<CookiesConsent>({
  necessary: true,
  performance: false,
  functional: false,
  advertising: false
});

export default function() {
  const { $logException, $axios } = useContext();
  const { dispatch } = useStore();
  const cookies = new Cookies();
  const tyCookie = cookies.get('ty-cookies');

  tyCookieExist.value = !!tyCookie;

  const setCookie = () => {
    const cookieObj = {
      ...toRaw(cookiesConsentState),
      visitor_guid: visitorGuid.value
    };
    const expireDate = new Date();
    expireDate.setMonth(expireDate.getMonth() + 24);
    cookies.set('ty-cookies', cookieObj, { path: '/', expires: expireDate });
  };

  const setWindowConsents = async() => {
    // @ts-ignore
    const rawCookiesConsents = toRaw(cookiesConsentState);
    // @ts-ignore
    window.cookiesConsents = { ...rawCookiesConsents };
    // @ts-ignore
    window.PubSub?.publish('cookiesConsents', window.cookiesConsents);
    // @ts-ignore
    await dispatch('cookiesConsent/UPDATE_COOKIES_CONSENT_STATE', rawCookiesConsents);
  };

  const setCookiesSettingsFromCookie = () => {
    if (!tyCookie) { return; }
    const { performance, functional, advertising, visitor_guid } = tyCookie;
    cookiesConsentState.performance = performance;
    cookiesConsentState.functional = functional;
    cookiesConsentState.advertising = advertising;
    visitorGuid.value = visitor_guid;

    setWindowConsents();
  };

  setCookiesSettingsFromCookie();

  const saveCookies = async(saveAllCookies?: boolean) => {
    if (saveAllCookies) {
      cookiesConsentState.performance = true;
      cookiesConsentState.functional = true;
      cookiesConsentState.advertising = true;
    }

    setWindowConsents();

    try {
      const data = await COOKIES.SAVE($axios, toRaw(cookiesConsentState), visitorGuid.value);
      // eslint-disable-next-line camelcase
      const { visitor_guid } = (data || {});
      // eslint-disable-next-line camelcase
      visitorGuid.value = visitor_guid;
      tyCookieExist.value = true;
      setCookie();
    } catch (e) {
      $logException(e);
    }
  };

  return {
    cookiesConsentState,
    tyCookieExist,
    visitorGuid,
    saveCookies
  };
}
