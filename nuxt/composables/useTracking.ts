import { getCurrentInstance, ref, onMounted, onBeforeUnmount, useContext } from '@nuxtjs/composition-api';
import { isNil } from 'lodash-es';
import Cookies from 'universal-cookie';
import { analyticsShelfTypes } from '@/utils/consts';

const defaultData = {
  eventType: 'NOEEC',
  eventFrameVersion: 1,
  eventValue: undefined,
  eventNonInteraction: false,
  eventParam_0: undefined,
  eventParam_1: undefined,
  eventParam_2: undefined,
  eventParam_3: undefined
};

interface Item {
  brand: string,
  category: string,
  id: number,
  price: string,
  variant: Array<any>,
  material: string,
  shelfType: number,
  configuratorType: number,
  physicalProductVersion: number,
  trackingList: string,
}

interface TrackData {
  eventLabel: string,
  eventCallback: () => {};
}

export const useTrackingDOM = () => {
  const vm = getCurrentInstance();

  if (!vm) {
    throw new Error('This must be called within a setup function.');
  }

  // @ts-ignore
  const { proxy: { $gtm, $route, $store } } = vm;
  const userId = $store.getters['global/USER_ID'];
  const sessionId = $store.getters['global/SESSION_ID'];
  const globalSessionId = $store.getters['global/GLOBAL_SESSION_ID'];

  const checkDomElement = (el: HTMLElement | SVGElement) => {
    if (process.env.NODE_ENV !== 'production') {
      if (!el || !(el instanceof HTMLElement || el instanceof SVGElement)) {
        console.error('first argument must be a DOM element');
      }
    }
  };

  const track = (el: HTMLElement, data: object) => {
    checkDomElement(el);

    const parent = el?.closest('[data-section]') as HTMLElement | null;
    const parentName = (parent && parent.dataset && parent.dataset.section) || 'Section name not found';

    $gtm.push({
      ...defaultData,
      event: 'userInteraction',
      eventCategory: $route?.name?.split('___')[0],
      eventAction: parentName,
      userId,
      sessionId: `sessionID-${sessionId}`,
      ...data
    });
  };

  const trackBoardLoaded = (list: String) => {
    $gtm.push({
      event: 'userInteraction',
      eventCategory: 'board_loaded',
      eventAction: list,
      eventLabel: undefined,
      userId,
      sessionId: `sessionID-${sessionId}`,
      ...defaultData
    });
  };

  const trackCookiebar = (eventAction: string, eventLabel: string, eventNonInteraction: boolean = false) => {
    const cookies = new Cookies();
    const globalSessionIdCookie = cookies.get('global_session_id');

    $gtm.push({
      ...defaultData,
      event: 'cookieBar',
      eventCategory: 'cookie_bar',
      eventAction,
      eventLabel,
      userId,
      sessionId,
      globalSessionId: globalSessionId || globalSessionIdCookie,
      eventNonInteraction
    });
  };

  return {
    track,
    trackBoardLoaded,
    trackCookiebar
  };
};

export const useTrackInteraction = (el: any) => {
  const { track } = useTrackingDOM();

  return (trackData: any) => {
    if (trackData && !isNil(trackData.eventLabel)) {
      track(el.value, {
        event: 'userInteraction',
        ...trackData
      });
    }
  };
};
