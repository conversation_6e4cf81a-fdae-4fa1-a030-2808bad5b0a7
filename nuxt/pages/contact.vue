<template>
  <main class="bg-[#edf0f0]">
    <div class="h-[300px] lg:h-[600px]">
      <BasePicture
        type="M T SD LD"
        path="contact/hero"
        picture-classes="w-full h-full object-cover"
        img-classes="w-full h-full object-cover"
        data-testid="contact-header-picture"
        v-bind="{
          isRetinaUploaded: false,
          alt: $t('contact.heading')
        }"
      />
    </div>
    <section class="container-cstm mx-auto grid grid-cols-12 " data-testid="contact-section">
      <div class="col-span-12 lg:col-span-10 lg:col-start-2 -mt-12 lg:mt-[-600px]">
        <h1
          class="h-[300px] hidden lg:flex items-center text-white bold-54 md:-mx-32"
          data-testid="contact-heading"
          v-html="$t('contact.heading')"
        />
        <div
          id="contact-form"
          class="rounded-6 bg-[#fff] -mx-16 px-16 md:-mx-32 md:px-32 pt-32 pb-16 lg:py-64 lg:px-[10%] lg:mb-128"
        >
          <Transition name="fade">
            <ContactSubmitedThankYou
              v-if="isFormSent"
              data-testid="contact-submited-thank-you"
              v-bind="{
                firstName: userData.firstName,
                email: userData.email
              }"
            />
            <div v-else>
              <ContactFaq class="mb-48 lg:mb-64" data-testid="contact-faq" />
              <h2
                class="mb-32 normal-24"
                data-testid="contact-topic-title"
                v-html="$t('contact.forms.topics.title')"
              />
              <Contact v-on:formSubmit="onFormSent" />
            </div>
          </Transition>
          <ContactFooter />
        </div>
      </div>
    </section>
  </main>
</template>

<script>
import { defineComponent, ref, useStore, useContext, computed } from '@nuxtjs/composition-api';
import { deepMerge } from '~/utils/helpers';
import { hashSHA256 } from '~/composables/useSha';

export default defineComponent({
  name: 'ContactPage',
  setup() {
    const { $gtm } = useContext();
    const store = useStore();
    const isFormSent = ref(false);
    const userData = ref({
      firstName: '',
      email: ''
    });
    const regionCode = computed(() => store.getters['global/REGION_CODE']);
    const userId = computed(() => store.getters['global/USER_ID']);

    const onFormSent = async(firstName, email, newsletter) => {
      isFormSent.value = true;
      userData.value.firstName = firstName;
      userData.value.email = email;

      if (!userId.value) {
        await store.dispatch('global/UPDATE_GLOBAL_AFTER_ACCOUNT_CREATION', null, { root: true });
      }

      if (newsletter) {
        $gtm.push({
          event: 'generate_lead',
          placement: 'contact_form',
          userId: userId.value,
          leadsUserData: {
            ea: hashSHA256(email),
            address: {
              country: regionCode.value
            }
          }
        });
      }
    };

    return {
      onFormSent,
      userData,
      isFormSent
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });

    return deepMerge(i18nHead, {
      title: this.$t('contact.meta.title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('contact.meta.description')
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.$t('common.category.all_meta_title')
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.$t('hp.meta.description')
        }
      ]
    });
  }
});
</script>
