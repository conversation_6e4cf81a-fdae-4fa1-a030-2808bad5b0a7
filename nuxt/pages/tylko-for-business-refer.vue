<template>
  <main
    id="mmWrapperLandingPage"
    class="flex items-center justify-center lg:min-h-[630px]"
  />
</template>

<script>
import { defineComponent, useContext } from '@nuxtjs/composition-api';
import { deepMerge } from '@/utils/helpers';
export default defineComponent({
  setup() {
    const { i18n, $config } = useContext();

    const locale = i18n.loadedLanguages[0].toLocaleUpperCase();

    const mentionMe = $config.dev
      ? {
          src: `https://tag-demo.mention-me.com/api/v2/referreroffer/mm640fd1b0?situation=landingpage&segment=designer-${locale}`
        }
      : {
          innerHTML: `window.get_load_mentionme = "?implementation=embed&situation=landingpage&segment=designer-${locale}"`,
          type: 'text/javascript',
          charset: 'utf-8'
        };

    return {
      mentionMe
    };
  },
  head() {
    const i18nHead = this.$nuxtI18nHead({ addSeoAttributes: true });

    return deepMerge(i18nHead, {
      title: this.$t('refer.meta.title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('refer.meta.description')
        },
        {
          hid: 'og:title',
          property: 'og:title',
          content: this.$t('refer.meta.title')
        },
        {
          hid: 'og:description',
          property: 'og:description',
          content: this.$t('refer.meta.description')
        }
      ],
      __dangerouslyDisableSanitizers: ['script'],
      script: [this.mentionMe]
    });
  }
});
</script>
