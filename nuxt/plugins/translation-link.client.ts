import { onGlobalSetup, useRouter } from '@nuxtjs/composition-api';
import { spaPages } from '~/utils/pages';
import useHeader from '~/composables/useHeader';

/*
The problem:
We want to handle redirects in translations. It works in Django:
    "i18n-django": "Go <a href='/'>home</a>",
but doesn't work in Nuxt (we need NuxtLink to keep SPA behavior):
    "i18n-nuxt": "Go <NuxtLink to='/'>home</NuxtLink>"

You can't just use it like this:
    <p v-html="$t('i18n-nuxt')" />
because <NuxtLink> become HTML tag like <div>

Solution:
We need to use <a> tags in translations but simulate NuxtLink behavior.
This plugin handles all click events on the page and checks if it's <a>. If so,
it gets the href attribute and passes to router function - resolve. It checks if that
href can be handled by <PERSON>uxt. If not, do nothing and let the browser redirect to
the Django page, otherwise prevent browser redirecting, and push href to vue
router, so it behaves as NuxtLink.
*/

const findAnchor = (element: EventTarget | null, deep: number) => {
  if (!element || !(element instanceof HTMLElement)) { return null; }

  let currentElement = element;
  let i = 0;

  while (!(currentElement instanceof HTMLAnchorElement)) {
    const parent = currentElement.parentElement;

    if (parent === null || i >= deep) {
      return null;
    }

    currentElement = parent;
    i += 1;
  }

  return currentElement;
};

export default function() {
  onGlobalSetup(() => {
    const router = useRouter();
    const {
      setHeaderDefaultState
    } = useHeader();

    const handleClickEvent = (event: Event) => {
      const anchor = findAnchor(event.target, 3);

      if (anchor) {
        const href = anchor.getAttribute('href');
        const targetAttr = anchor.getAttribute('target');

        if (!href || targetAttr === '_blank') { return; }

        const resolvedRouteName = router.resolve(href)?.resolved?.name;
        const currentRouteName = router.currentRoute?.name;

        const removeLangSuffix = (name:string) => name.replace(/___.{2,}/g, '');

        const hasLanguageChanged = () => {
          const newRouteNameWithoutLangSuffix = resolvedRouteName && removeLangSuffix(resolvedRouteName);
          const currentRouteNameWithoutLangSuffix = currentRouteName && removeLangSuffix(currentRouteName);

          return currentRouteNameWithoutLangSuffix === newRouteNameWithoutLangSuffix;
        };

        if (hasLanguageChanged() === false && resolvedRouteName && spaPages.includes(removeLangSuffix(resolvedRouteName))) {
          event.preventDefault();

          // Close navigation
          setHeaderDefaultState();

          setTimeout(() => {
            router.push(href);
          }, 300);
        }
      }
    };

    document.addEventListener('click', handleClickEvent);
  });
}
