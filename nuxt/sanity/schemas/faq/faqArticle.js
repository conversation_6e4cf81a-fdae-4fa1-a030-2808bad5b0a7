export default {
  name: 'faqArticle',
  title: 'FAQ Article',
  type: 'document',
  preview: {
    select: {
      title: 'title'
    }
  },
  fields: [
    {
      name: 'title',
      type: 'string',
      title: 'title',
      description: 'title',
      validation: Rule => Rule.required()
    },
    {
      name: 'category',
      title: 'Category',
      type: 'reference',
      to: [
        { type: 'faqCategory' }
      ],
      validation: Rule => Rule.required()
    },
    {
      name: 'order',
      type: 'number',
      title: 'order ID',
      description: 'order ID, the order is sorting by it',
      validation: Rule => Rule.required()
    },
    {
      name: 'slug',
      type: 'string',
      title: 'Page slug URL',
      description: 'Page url slug',
      validation: Rule => Rule.required()
    },
    {
      name: 'content',
      type: 'markdown',
      title: 'Page content',
      validation: Rule => Rule.required()
    },
    {
      name: 'tags',
      type: 'array',
      title: 'tags',
      description: 'tags',
      // validation: Rule => Rule.required(),
      of: [
        {
          name: 'id',
          type: 'reference',
          to: [
            { type: 'faqTag' }
          ]
        }
      ],
    },
    {
      name: 'showRating',
      type: 'boolean',
      title: 'showRating',
      description: 'showRating',
      initialValue: false,
      // validation: Rule => Rule.required()
    },
  ]
};
