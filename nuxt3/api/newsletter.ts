export const subscribe = (email: string, source: string = 'content', waitlistOnly: boolean = false) => $fetch('/api/v1/newsletter/', {
  method: 'post',
  params: {
    source: `${source}${waitlistOnly ? '&waitlistOnly' : ''}`
  },
  ignoreResponseError: true,
  body: {
    email
  }
});

export interface NewsletterData {
  email?: string | null,
  phonePrefix?: string | null,
  phone?: string | null,
  source: string,
}

export const SUBSCRIBE_NEWSLETTER = (newsletterData: NewsletterData) => useApi('/api/v1/newsletter/', {
  method: 'post',
  ignoreResponseError: true,
  body: {
    email: newsletterData.email,
    phonePrefix: newsletterData.phonePrefix,
    phone: newsletterData.phone,
    source: newsletterData.source
  }
});
