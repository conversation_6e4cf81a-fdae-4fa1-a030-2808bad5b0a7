<template>
  <div>
    <Transition name="fade">
      <div v-show="formLoaded && isFormMounted">
        <FormKit
          v-model="formValues"
          type="form"
          v-bind:actions="false"
          v-on:submit="submitForm"
          v-on:submit-invalid="failedValidation"
        >
          <div class="md:flex md:gap-16 md:mb-16">
            <FormKit
              type="text"
              name="firstName"
              v-bind:validation="'required'"
              autocomplete="given-name"
              v-bind:placeholder="$t('checkout.form.first_name.placeholder')"
              v-bind:label="$t('checkout.form.first_name.label')"
              outer-class="md-max:mb-16 md:w-full"
              v-bind:validation-messages="{
                required: $t('checkout.form.first_name.required'),
              }"
            />
            <FormKit
              type="text"
              name="lastName"
              validation="required"
              autocomplete="family-name"
              v-bind:placeholder="$t('checkout.form.last_name.placeholder')"
              v-bind:label="$t('checkout.form.last_name.label')"
              outer-class="md-max:mb-16 md:w-full"
              v-bind:validation-messages="{
                required: $t('checkout.form.last_name.required'),
              }"
            />
          </div>
          <div
            v-show="global.regionName === 'united_kingdom'"
            class="mb-16"
          >
            <CheckoutFetchify
              v-on:click-to-address-selected="handleClickToAddress"
            />
          </div>
          <FormKit
            outer-class="mb-16"
            type="text"
            name="streetAddress1"
            autocomplete="street-address"
            validation="required"
            v-bind:validation-messages="{
              required: $t('checkout.form.street_address.required'),
            }"
            v-bind:label="$t('checkout.form.street_address.label')"
            v-bind:placeholder="$t('checkout.form.street_address.placeholder')"
          />
          <UiCollapseBox
            v-model="additionalAddressBoxVisible"
            class="max-h-[26px] mb-32 pb-4"
            v-bind="{
              selector: 'p',
              variant: 'left',
              maxHeightClass: '!max-h-[200px]',
              selectorClasses: 'ty-link-m',
              overlayClasses: '',
              title: $t('checkout.form.street_address_2.collapse_box.title'),
              'data-testid': `checkout-collapse-address_2`
            }"
          >
            <Transition name="fade">
              <FormKit
                v-show="additionalAddressBoxVisible"
                type="text"
                outer-class="mt-16"
                name="streetAddress2"
                v-bind:placeholder="$t('checkout.form.street_address_2.label')"
                v-bind:label="$t('checkout.form.street_address_2.label')"
              />
            </Transition>
          </UiCollapseBox>
          <FormKit
            type="select"
            name="floorNumber"
            outer-class="mb-16 md:w-1/2 md:pr-8"
            v-bind:label="$t('checkout.form.floor_number.label')"
            v-bind:options="{
              '0': $t('checkout.dropdown.answer.floor.0.house'),
              '1': '1',
              '2': '2',
              '3': '3',
              '4': '4',
              '5': $t('checkout.form.floor_number.options.5'),
            }"
            v-bind:placeholder="$t('checkout.form.floor_number.placeholder')"
            v-on:input="handleFloorOption"
            v-on:click="handleFloorSelect"
          />
          <template v-if="formValues.floorNumber && formValues.floorNumber > 0">
            <p
              class="normal-16"
              v-html="$t('checkout.form.elevator.label')"
            />
            <FormKit
              type="radio"
              name="noElevator"
              outer-class="mt-16 mb-32 elevator-radio"
              v-bind:options="{
                yes: $t('checkout.form.elevator.options.yes'),
                no: $t('checkout.form.elevator.options.no'),
              }"
            />
          </template>
          <div class="md:flex md:gap-16 md:mb-16">
            <FormKit
              type="text"
              name="postalCode"
              autocomplete="postal-code"
              validation="required|isDeliveryAvailable"
              v-bind:validation-rules="{ isDeliveryAvailable: async ({ value }) => await validatePostalCodeDelivery(value as string) }"
              v-bind:validation-messages="{
                required: $t('checkout.form.city.required'),
                isDeliveryAvailable: $t('checkout.form.postal_code.delivery_not_available'),
              }"
              maxlength="20"
              v-bind:placeholder="$t('checkout.form.postal_code.label')"
              v-bind:label="$t('checkout.form.postal_code.label')"
              outer-class="md-max:mb-16 md:w-full"
            />
            <FormKit
              type="text"
              name="city"
              validation="required"
              v-bind:validation-messages="{
                required: $t('checkout.form.city.required'),
              }"
              v-bind:placeholder="$t('checkout.form.city.placeholder')"
              v-bind:label="$t('checkout.form.city.label')"
              outer-class="md-max:mb-16 md:w-full"
            />
          </div>

          <CheckoutInputTooltipWrapper
            v-bind="{
              disableTooltip: !cartStore.hasT03,
            }"
          >
            <template #default>
              <InputSelectChangeCountry
                v-bind="{
                  label: $t('checkout.billing.dropdown.label.country'),
                  placeholder: $t('checkout.choose_your_country_palceholder'),
                  name: 'region',
                  validation:'required',
                  disabled: cartStore.hasT03,
                  region: formValues.region
                }"
              />
            </template>
            <template #tooltipBody>
              <p v-html="$t('checkout.select_region_tooltip')" />
            </template>
          </CheckoutInputTooltipWrapper>
          <FormKit
            type="textarea"
            name="notes"
            outer-class="w-full mt-16"
            v-bind:placeholder="$t('checkout.billing.delivery_instructions.placeholder')"
            v-bind:label="$t('checkout.billing.delivery_instructions.label')"
            v-bind:maxlength="additionalInfoMaxLength"
          />
          <p
            v-if="formValues.notes"
            class="mt-4 text-right normal-14"
          >
            {{ formValues.notes.length }} / {{ additionalInfoMaxLength }}
          </p>
          <h2
            id="contact"
            class="mt-32 mb-16 bold-20 lg:bold-24 text-offblack-800 lg:mt-48"
            v-html="$t('checkout.contact_details.headline')"
          />

          <FormKit
            type="email"
            name="email"
            outer-class="mb-16"
            validation="required|email"
            autocomplete="on"
            v-bind:placeholder="$t('checkout.form.email.placeholder')"
            v-bind:label="$t('checkout.form.email.label')"
            v-bind:validation-messages="{
              required: $t('checkout.form.email.required_nuxt3'),
              email: $t('checkout.form.email.required_nuxt3')
            }"
          />
          <div class="flex mb-16">
            <CheckoutPhonePrefixSelect
              v-model="phonePrefixCode"
              label="code"
            />
            <FormKit
              type="tel"
              name="phone"
              validation="required"
              v-bind:placeholder="$t('checkout.form.phone_number.placeholder')"
              v-bind:label="$t('checkout.form.phone_number.label')"
              v-bind:validation-messages="{
                required: $t('checkout.form.phone_number.required'),
              }"
              outer-class="w-full ml-16"
              autocomplete="tel-national"
            />
          </div>
          <UiCollapseBox
            v-model="alternativePhoneNumberVisible"
            class="max-h-[26px] mb-32 pb-4"
            v-bind="{
              selector: 'p',
              variant: 'left',
              maxHeightClass: '!max-h-[200px]',
              selectorClasses: 'ty-link-m',
              overlayClasses: '',
              title: $t('checkout.form.alternative_phone_number.label'),
              'data-testid': `checkout-collapse-alternative_phone_number`
            }"
            v-on:input="resetAlternativePhoneNumber"
          >
            <Transition name="fade">
              <FormKit
                v-show="alternativePhoneNumberVisible"
                type="text"
                outer-class="mt-16"
                name="invoicePhone"
                v-bind:validation="alternativePhoneNumberVisible? 'required' : ''"
                autocomplete="tel"
                v-bind:placeholder="$t('checkout.form.alternative_phone_number.label')"
                v-bind:label="$t('checkout.form.alternative_phone_number.label')"
              />
            </Transition>
          </UiCollapseBox>

          <h2
            id="billing"
            class="my-32 bold-20 lg:bold-24 text-offblack-800 lg:mt-48"
            v-html="$t('checkout.invoice_details.heading')"
          />

          <FormKit
            type="checkbox"
            name="sameAsDelivery"
            v-bind:label="$t('checkout.billing.same_as_delivery.label')"
            outer-class="mt-16 mb-32 h-[20px]"
            label-class="pl-16 cursor-pointer normal-16"
          />

          <div v-show="!formValues.sameAsDelivery">
            <FormKit
              v-model="B2B"
              v-bind:options="{
                regular: $t('checkout.form.client_tab.private'),
                B2B: $t('checkout.form.client_tab.company')
              }"
              type="radio"
              outer-class="radio-b2b mb-32"
            />
            <div v-show="vatNumberAndCompanyNameVisible">
              <FormKit
                type="text"
                name="vat"
                validation="required"
                outer-class="mb-16"
                v-bind:validation-rules="{
                  required: vatNumberAndCompanyNameVisible,
                }"
                v-bind:validation-messages="{
                  required: $t('delivery_time_frames.choose_dates_modal.error_field_required'),
                }"
                v-bind:placeholder="$t('checkout.form.vat.placeholder')"
                v-bind:label="$t('checkout.form.vat.label')"
                autocomplete="on"
              />
              <FormKit
                type="text"
                name="companyName"
                autocomplete="organization"
                outer-class="mb-16"
                v-bind:placeholder="$t('checkout.form.company_name.placeholder')"
                v-bind:label="$t('checkout.form.company_name.label')"
              />
            </div>
            <div class="md:flex md:gap-16 md:mb-16">
              <FormKit
                type="text"
                name="invoiceFirstName"
                outer-class="md-max:mb-16 md:w-full"
                v-bind:validation="!formValues.sameAsDelivery ? 'required': ''"
                validation-behavior="live"
                autocomplete="given-name"
                v-bind:label="$t('checkout.form.first_name.label')"
                v-bind:placeholder="$t('checkout.form.first_name.placeholder')"
                v-bind:validation-messages="{
                  required: $t('checkout.form.first_name.required'),
                }"
              />
              <FormKit
                type="text"
                name="invoiceLastName"
                outer-class="md-max:mb-16 md:w-full"
                v-bind:validation="!formValues.sameAsDelivery ? 'required' : ''"
                validation-behavior="live"
                autocomplete="family-name"
                v-bind:placeholder="$t('checkout.form.last_name.placeholder')"
                v-bind:label="$t('checkout.form.last_name.label')"
                v-bind:validation-messages="{
                  required: $t('checkout.form.last_name.required'),
                }"
              />
            </div>
            <FormKit
              type="text"
              name="invoiceStreetAddress1"
              outer-class="mb-16"
              autocomplete="street-address"
              v-bind:validation="!formValues.sameAsDelivery ? 'required' : ''"
              validation-behavior="live"
              v-bind:placeholder="$t('checkout.form.street_address.placeholder')"
              v-bind:label="$t('checkout.form.street_address.label')"
              v-bind:validation-messages="{
                required: $t('checkout.form.street_address.required'),
              }"
            />
            <div class="md:flex md:gap-16 md:mb-16">
              <FormKit
                type="text"
                name="invoicePostalCode"
                outer-class="md-max:mb-16 md:w-full"
                v-bind:validation="!formValues.sameAsDelivery ? 'required' : ''"
                autocomplete="postal-code"
                maxlength="20"
                validation-behavior="live"
                v-bind:placeholder="$t('checkout.form.postal_code.label')"
                v-bind:label="$t('checkout.form.postal_code.label')"
                v-bind:validation-messages="{
                  required: $t('checkout.form.postal_code.required'),
                }"
              />
              <FormKit
                type="text"
                name="invoiceCity"
                outer-class="md-max:mb-16 md:w-full"
                v-bind:validation="!formValues.sameAsDelivery ? 'required' : ''"
                validation-behavior="live"
                v-bind:placeholder="$t('checkout.form.city.placeholder')"
                v-bind:label="$t('checkout.form.city.label')"
                v-bind:validation-messages="{
                  required: $t('checkout.form.city.required'),
                }"
              />
            </div>
          </div>
          <div
            v-show="!formValues.sameAsDelivery"
          >
            <InputSelectChangeCountry

              outer-class="mb-32 md:mb-[72px]"
              v-bind="{
                label: $t('checkout.billing.dropdown.label.country'),
                placeholder: $t('checkout.choose_your_country_palceholder'),
                name: 'invoiceCountry',
                validation:'required',
                region: formValues.invoiceCountry ? formValues.invoiceCountry : formValues.region
              }"
            />
          </div>

          <CheckoutDeliveryAddressSummary
            v-if="formValues.sameAsDelivery && formValues.firstName && formValues.lastName && formValues.streetAddress1 && formValues.postalCode && formValues.city && formValues.region"
            class="mb-32 md:mb-96"
            v-bind="{
              firstName: formValues.firstName,
              lastName: formValues.lastName,
              streetAddress1: formValues.streetAddress1,
              streetAddress2: formValues.streetAddress2,
              postalCode: formValues.postalCode,
              city: formValues.city,
              region: formValues.region,
            }"
          />

          <slot v-if="!preferencesTest" />

          <h2
            v-if="preferencesTest"
            id="my_preferences"
            class="bold-20 lg:bold-24 text-offblack-800 my-32 lg:mt-48"
            v-html="$t('checkout.my_preferences.heading')"
          />
          <!-- newsletter -->
          <FormKit
            type="checkbox"
            name="newsletter"
            outer-class="mt-16 mb-32"
          >
            <template #label>
              <span
                class="normal-16 pl-16 cursor-pointer"
                v-html="$t('newsletter.consent-checkout')"
              />
            </template>
          </FormKit>
          <!-- occasionalMessages -->
          <FormKit
            v-if="preferencesTest"
            type="checkbox"
            name="occasionalMessages"
            outer-class="mt-16 mb-32"
            v-bind:label="$t('checkout.form.occasional_messages.label')"
            label-class="normal-16 pl-16 cursor-pointer"
          />
          <slot v-if="preferencesTest" />
          <div
            v-if="preferencesTest"
            class="flex mt-16 mb-24"
          >
            <!-- termsOfService -->
            <div ref="termsOfServiceDiv">
              <FormKit
                type="checkbox"
                name="termsOfService"
                outer-class="mt-16 mb-32"
                validation="required"
                v-bind:validation-messages="{
                  required: $t('checkout.form.first_name.required'),
                }"
              >
                <template #label>
                  <span class="normal-16 pl-16 cursor-pointer">
                    {{ $t('checkout.form.terms_of_service.label') }}
                    <BaseLink
                      ref="termsOfServiceLinkDiv"
                      variant="link-underlined"
                      class="ml-4 -mt-1 text-orange lowercase h-24"
                      v-bind="{
                        href: $addLocaleToPath('terms'),
                        trackData: {}
                      }"
                    >
                      {{ $t('checkout.form.terms_of_service.link') }}
                    </BaseLink>
                  </span>
                </template>
              </FormKit>
            </div>
          </div>
          <FormKit
            type="submit"
            v-bind:input-class="`lg-max:w-full btn-cta relative ${submitButtonIsLoading ? '!text-orange !bg-orange' : 'text-white'}`"
            v-bind:disabled="submitButtonIsLoading"
            v-bind:label="$t('checkout.form.submit')"
          >
            {{ $t('checkout.form.submit') }}
            <UiDotsLoader
              v-if="submitButtonIsLoading"
              class="mt-2"
              bounce-class="bg-white"
            />
          </FormKit>
          <p
            class="mt-32 normal-12 text-grey-900 copy-with-link"
            v-html="$t('checkout.form.datacontroller.label')"
          />
        </FormKit>
        <CheckoutSwitchRegionModal
          v-model="checkoutSwitchRegionModal"
          v-bind:default-close="true"
          v-on:on-success="onSuccess"
        />
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { getValidationMessages } from '@formkit/validation';
import { useDebounceFn } from '@vueuse/core';
import { checkoutRegions } from '~/composables/checkout/regions';
import { handleFormValues } from '~/composables/checkout/handleFormValues';
import scrollToElement from '~/helpers/scrollToElement';
import { useScartStore } from '~/stores/scart';

const route = useRoute();

const global = useGlobal();
const cartStore = useScartStore();

const { regionCode, cartId } = storeToRefs(global);

const gtm = useGtm();
const additionalInfoMaxLength = 25;
const fakeOnMounted = ref(false);
const termsOfServiceDiv = ref(null);
const termsOfServiceLinkDiv = ref(null);
const checkout2024 = useFeatureFlag('checkout2024');
const preferencesTest = computed(() => ['UK', 'FR', 'ES', 'BE', 'AT', 'SE', 'NO', 'DK', 'FI', 'LU'].includes(regionCode.value?.toUpperCase()) ? checkout2024 : false);
const {
  formValues,
  submitForm,
  submitButtonIsLoading,
  phonePrefixCode,
  vatNumberAndCompanyNameVisible,
  alternativePhoneNumberVisible,
  resetAlternativePhoneNumber,
  additionalAddressBoxVisible,
  formLoaded,
  isFormMounted,
  B2B
} = await handleFormValues();

const handleClickToAddress = ({
  city,
  postalCode,
  streetAddress1,
  streetAddress2
}) => {
  additionalAddressBoxVisible.value = !!streetAddress2;
  formValues.value.city = city;
  formValues.value.postalCode = postalCode;
  formValues.value.streetAddress1 = streetAddress1;
  formValues.value.streetAddress2 = streetAddress2;
};

const handleFloorSelect = () => {
  gtm.push({
    event: 'userInteraction',
    eventType: 'EEC',
    eventCategory: 'checkout',
    eventAction: 'Floor_number',
    eventLabel: 'open'
  });
};

const handleFloorOption = () => {
  gtm.push({
    event: 'userInteraction',
    eventType: 'EEC',
    eventCategory: 'checkout',
    eventAction: 'Floor_number',
    eventLabel: formValues.value.floorNumber
  });
};

const { checkoutSwitchRegionModal, onSuccess } = checkoutRegions(formValues, false);

onUpdated(() => {
  if (fakeOnMounted.value) { return; }
  fakeOnMounted.value = true;
  const { section } = route.query;

  if (section && document.querySelector(`#${section}`)) {
    scrollToElement({ element: `#${section}`, offset: 32 });
  }
});

const failedValidation = (node) => {
  const list = getValidationMessages(node);
  const element = document.querySelector(`[name="${[...list][0][0].name}"]`);
  scrollToElement({ element, offset: 64 });
};

const validatePostalCodeDelivery = useDebounceFn(async (postalCode) => {
  const response = await $fetch<{ valid:boolean }>('/api/v2/postal-code-validation/', {
    method: 'GET',
    retry: 0,
    ignoreResponseError: true,
    params: {
      postal_code: postalCode.trim(),
      cart_id: cartId.value
    }
  });

  return response?.valid;
}, 500);

</script>

<style lang="scss">

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.copy-with-link {
  a {
    @apply underline;
  }
}

.radio-b2b,
.elevator-radio {
   .formkit-options {
       @apply flex gap-32 h-24;
   }
  .formulate-input-wrapper {
    @apply flex gap-8 leading-none;
  }

  .formulate-input-group-item {
    .formulate-input-wrapper {
      @apply flex items-center gap-8;
    }

    .formulate-input-element {
      @apply p-8 gap-0;
    }
  }
}
.formkit-form {
    > .formkit-messages{
        display: none;
    }
}
</style>
