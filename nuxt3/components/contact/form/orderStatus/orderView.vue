<template>
  <div>
    <ContactFormOrderStatusItem
      v-for="(item, index) in orderItems"
      v-bind:key="index"
      v-bind="{
        data: item,
        isFirstOrderItem: index === 0,
        isLastOrderItem: index === orderItems.length - 1,
        isOrderPaid,
        isComplaint,
        isAssemblyServiceMix
      }"
      v-on="{
        toggleAssemblyModal: (itemData: any) => {
          assemblyModalData = itemData,
          isAssemblyModalVisible = true
        },
        toggleFaqModal: () => isFaqModalVisible = true
      }"
    />

    <ModalOrderStatusCheckerFaq
      v-model="isFaqModalVisible"
      v-on="{
        toggleFaqModal: () => isFaqModalVisible = !isFaqModalVisible
      }"
    />

    <ModalOrderStatusCheckerNotLoggedIn
      v-model="isNotLoggedInModalVisible"
      v-on="{
        toggleNotLoggedInModal: () => isNotLoggedInModalVisible = !isNotLoggedInModalVisible
      }"
    />

    <ModalOrderStatusCheckerAssemblyPackages
      v-model="isAssemblyModalVisible"
      v-bind="{
        data: assemblyModalData
      }"
      v-on="{
        toggleAssemblyModal: () => isAssemblyModalVisible = !isAssemblyModalVisible
      }"
    />
  </div>
</template>

<script lang="ts" setup>
defineProps({
  isOrderPaid: {
    type: Boolean,
    required: true
  },
  isComplaint: {
    type: Boolean,
    required: true
  },
  orderItems: {
    type: Array,
    required: true
  },
  isAssemblyServiceMix: {
    type: Boolean,
    required: true
  }
});

const assemblyModalData = ref({});
const isFaqModalVisible = ref(false);
const isAssemblyModalVisible = ref(false);
const isNotLoggedInModalVisible = ref(false);
</script>
