<template>
  <section class="relative overflow-hidden pdp-gallery">
    <p
      v-if="colorLabel"
      class="absolute px-8 py-4 bg-white/80 right-8 top-8 text-neutral-900 normal-12 z-1"
      v-html="colorLabel"
    />

    <BaseCarousel
      class="pdp-gallery__carousel"
      data-pdp-gallery-images-container
      v-bind="{
        options: SWIPER_OPTIONS,
        name: 'pdpGalleryCarousel',
        swiperRootClasses: 'w-full'
      }"
    >
      <BaseCarouselSlide
        v-for="item in items"
        v-bind:key="item.id"
        class="cursor-pointer"
      >
        <div
          v-if="item.animation && item.animation.desktop"
          class="w-full h-full aspect-square"
        >
          <BasePicture
            class="top-0 left-0 w-full h-full absolute"
            data-testid="pdp-mosaic-tile-img"
            v-bind="{
              path: item.image.path,
              type: item.image.type,
              alt: item.image.alt || (item.textBlock && item.textBlock.title && $t(item.textBlock.title)) || '',
              imgClasses: [
                'object-cover object-center w-full h-full',
                item.mobileAspectRatioClass
              ]
            }"
          />
          <BasePicture
            v-for="slide in [1, 2]"
            v-bind:key="slide"
            class="top-0 left-0 w-full h-full absolute fadein"
            v-bind:class="`fadein--${slide}`"
            data-testid="pdp-mosaic-tile-img"
            v-bind="{
              path: `${item.image.path}/${slide}`,
              type: item.image.type,
              alt: item.image.alt || (item.textBlock && item.textBlock.title && $t(item.textBlock.title)) || '',
              imgClasses: [
                'object-cover object-center w-full h-full',
                item.mobileAspectRatioClass
              ]
            }"
          />
        </div>

        <div
          v-else-if="item.video"
          class="w-full h-full aspect-square"
          v-bind:class="item.mobileAspectRatioClass"
        >
          <ClientOnly>
            <BaseVideoWistia
              class="!p-0 !absolute h-full w-full"
              v-bind="{
                videoId: {
                  desktop: item.video.desktop,
                  mobile: item.video.mobile
                },
                embedOptions: {
                  fitStrategy: 'cover'
                }
              }"
            />
          </ClientOnly>
        </div>

        <BasePicture
          v-else
          type="M D"
          data-testid="pdp-mobile-gallery-main-picture"
          v-bind="{
            path: item.image.path,
            alt: item.image.alt,
            pictureClasses: ['w-full'],
            imgClasses: ['object-cover', 'w-full', 'aspect-square']
          }"
        />
      </BaseCarouselSlide>
    </BaseCarousel>

    <div class="flex justify-center mt-8 md:mt-16">
      <BaseCarousel
        class="w-full mx-auto pdp-gallery__pagination"
        v-bind="{
          options: SWIPER_OPTIONS_PAGINATION,
          isNavigation: false,
          name: 'pdpGalleryCarouselPagination',
        }"
      >
        <BaseCarouselSlide
          v-for="(item, index) in items"
          v-bind:key="item.id"
          class="cursor-pointer !w-[calc(100%/4.5)] pl-4"
        >
          <BasePicture
            type="M D"
            data-testid="pdp-mobile-gallery-carousel-picture"
            v-bind="{
              path: item.image.path,
              alt: item.image.alt,
              imgClasses: ['aspect-square', 'md:aspect-[4/3]', activeSlideIndex === index && 'pdp-gallery__pagination--active']
            }"
          />
        </BaseCarouselSlide>
      </BaseCarousel>
    </div>
  </section>
</template>

<script lang="ts" setup>
import type { Swiper } from 'swiper';
import type { SwiperOptions } from 'swiper/types';
import { getTranslatedColor } from '~/composables/useColorName';

export interface Item {
  id: string
  mobileAspectRatioClass: string,
  analyticsLabel: string,
  image?: {
    path: string,
    alt: string
    type: 'A' | 'M T' | 'M D' | 'M T SD LD' | 'M T SD LD XLD'
  },
  animation?: {
    desktop: boolean,
    mobile: boolean
  },
  url?: string;
  textBlock?: {
    title?: string;
    label?: string;
    linkText?: string;
    titleClass?: string;
  },
  modalSlides?: {
    title: string
    body: string
    imagePath: string
  }[]
}

const props = defineProps({
  items: {
    type: Array as PropType<Item[]>,
    required: true
  },
  colorLabelKey: {
    type: [String, Array],
    required: true
  }
});

const emit = defineEmits(['imageClick']);

const $gtm = useGtm();

let carouselMain: Swiper;
let carouselPagination: Swiper;

const activeSlideIndex = ref(0);

const colorLabel = computed(() => getTranslatedColor(props.colorLabelKey));

const SWIPER_OPTIONS: SwiperOptions = {
  on: {
    beforeInit: (swiper: Swiper) => {
      carouselMain = swiper;
    },
    click: (swiper: Swiper) => {
      emit('imageClick', swiper.realIndex);
    },
    slideChange (swiper: Swiper) {
      activeSlideIndex.value = swiper.realIndex;
      carouselPagination.slideTo(activeSlideIndex.value);
    }
  }
} as const;

const SWIPER_OPTIONS_PAGINATION: SwiperOptions = {
  slidesPerView: 'auto',
  slideToClickedSlide: true,
  on: {
    beforeInit: (swiper: Swiper) => {
      carouselPagination = swiper;
    },
    click: (swiper: Swiper) => {
      carouselMain.slideToLoop(swiper.clickedIndex);
      switchImage();
    },
    slideChange (swiper: Swiper) {
      activeSlideIndex.value = swiper.realIndex;
      carouselMain.slideTo(activeSlideIndex.value);
    }
  }
};

const switchImage = () => {
  $gtm && $gtm.push({
    event: 'userInteraction',
    eventType: 'NOEEC',
    eventCategory: 'pdp_clicks',
    eventAction: 'Gallery',
    eventLabel: 'Miniature'
  });
};

</script>

<style lang="scss">
.pdp-gallery {
  &__carousel {
    .swiper {
      @screen md {
        @apply overflow-visible;
      }

      &:not(.swiper-initialized) {
        .swiper-wrapper {
          @apply mx-auto;
        }
      }
    }

    .swiper-wrapper {
      @screen md {
        --swiper-wrapper-col-span: 12;

        width: 100% !important;
      }

      @apply col-span-12 col-start-1;
    }

    .swiper-slide {
      padding: 0;
    }

    .swiper-button-prev,
    .swiper-button-next {
      opacity: 0;
    }
  }

  &__pagination {
    &--active {
      outline: solid 2px theme('colors.neutral.750');
      outline-offset: -2px;
    }

    .swiper-initialized {
      @apply w-full;
    }
  }

  &-swatches {
    &__carousel {
      overflow-y: hidden!important;
    }
  }

  .lightbox {
    @apply fixed top-0 left-0 mx-auto w-full h-screen bg-offwhite-600 overflow-hidden p-128;
  }
}
</style>
