<template>
  <div
    v-if="data"
    class="relative w-full"
  >
    <div class="aspect-[393/805] md:aspect-[768/896] lg:aspect-[1025/572] lg2:aspect-[640/357] xl:aspect-[9/5] xl2:aspect-[1728/960]">
      <section class="bg-beige-100 absolute inset-0 overflow-hidden py-48 lg:py-32 xl:py-48">
        <BasePicture
          class="absolute top-0 left-0 w-full h-full object-cover"
          v-bind="{
            path: `pdp/sofa/samples/${currentVariant > 21006 ? 'corduroy' : 'wool'}/${currentVariant}`,
            alt: $t('pdp.sofa.samples.title'),
            type: 'M T SD LD XLD',
            isRetinaUploaded: false
          }"
        />
        <div class="grid-container relative z-1 text-center flex flex-col justify-between h-full">
          <aside>
            <h3 class="text-neutral-750 semibold-12 md:semibold-14 lg:semibold-16 xl:semibold-18 text-center uppercase">
              {{ $t('pdp.sofa.samples.tagline') }}
            </h3>
            <h2 class="text-neutral-900 semibold-28 md:semibold-32 lg:semibold-44 xl:semibold-54 mt-8 lg:mt-24 text-center">
              {{ $t('pdp.sofa.samples.title') }}
            </h2>
            <BaseButton
              class="mt-16 lg:mt-24 lg:min-h-64 lg:min-w-[243px] lg:rounded-[48px]"
              data-testid="pdp-samples-open-button"
              v-bind="{
                variant: 'outlined',
                trackData: {
                  eventLabel: 'Open Samples Drawer'
                }
              }"
              v-on:click="isDrawerOpen = true"
            >
              {{ $t('pdp.sofa.samples.cta') }}
            </BaseButton>
          </aside>
          <aside class="text-neutral-900">
            <p class="normal-16">
              <b class="semibold-16">{{ $t('scart.item.label.color') }}</b> {{ $t(currentColor) }}
            </p>
            <p class="mt-2 normal-16">
              <b class="semibold-16">{{ $t('common.fabric.title') }}</b> {{ $t(currentVariant > 21006 ? 'common.fabric.corduroy' : 'common.fabric.wool') }}
            </p>
            <ul class="lg:bg-[rgba(231,227,223,0.50)] rounded-[100px] flex flex-wrap gap-4 mx-auto max-w-fit p-8 justify-center mt-8 md:mt-16">
              <li
                v-for="set in data"
                v-bind:key="set.variant_type"
                class="rounded-full w-48 h-48 flex items-center justify-center border border-transparent overflow-hidden cursor-pointer"
                v-bind:class="{ '!border-neutral-900': currentVariant === set.variant_type }"
                v-on:click="currentVariant = set.variant_type; currentColor = set.name_key;"
              >
                <BasePicture
                  class="overflow-hidden rounded-full"
                  img-classes="rounded-full max-w-[36px] min-w-[36px] h-[36px]"
                  v-bind="{
                    path: `lp/sample/sets/${set.variant_type}`,
                    alt: $t(set.name_key),
                    type: 'A'
                  }"
                />
              </li>
            </ul>
          </aside>
        </div>
        <PdpDrawerSofaSamples v-model="isDrawerOpen" />
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SAMPLES } from '~/api/samples';
import type { Sample } from '~/pages/samples.vue';

const { regionName } = useGlobal();
const { data: samplesData } = await SAMPLES(regionName, 10);

const data = computed(() => samplesData.value as Sample[]);

const isDrawerOpen = ref(false);
const currentColor = ref(data.value && data.value[0]?.name_key);
const currentVariant = ref(data.value && data.value[0]?.variant_type);

onMounted(() => {
  if (window.PubSub) {
    window.PubSub.subscribe('openSamplesDrawer', () => {
      isDrawerOpen.value = true;
    });
  }
});

onBeforeUnmount(() => {
  if (window.PubSub) {
    window.PubSub.unsubscribe('openSamplesDrawer');
  }
});
</script>
