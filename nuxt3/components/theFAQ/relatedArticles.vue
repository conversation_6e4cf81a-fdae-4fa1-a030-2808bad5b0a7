<template>
  <div
    class="lg:hidden before:h-1 before:bg-grey-700 before:my-16 before:block after:h-[1px] after:block after:bg-grey-700 after:mt-16 last:after:hidden"
  >
    <h3
      class="bold-20 text-offbblack-700"
      v-html="$t('faq.other_topics')"
    />
    <ul class="text-offblack-600">
      <li
        v-for="(item, index) in articles"
        v-bind:key="index"
      >
        <BaseLink
          v-bind:to="linkVariant === 'article'
            ? $addLocaleToPath('faq') + `articles/${route.params.category}/${item.slug}`
            : $addLocaleToPath('faq') + `tags/${route.params.tags}/${item.slug}`"
          class="flex justify-between items-center mb-24 first:mt-16"
          variant="link"
          v-bind:track-data="{}"
        >
          <h4
            class="normal-16"
            v-html="item.title"
          />
          <IconArrow class="rotate-180 w-8 h-[14px] ml-4" />
        </BaseLink>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>

const route = useRoute();

withDefaults(defineProps<{
    linkVariant: string;
    articles: FaqArticle[];
  }>(), {
  articles: () => []
});

</script>
