<template>
  <div class="spinner">
    <div
      class="bounce1"
      v-bind:class="bounceClass"
    />
    <div
      class="bounce2"
      v-bind:class="bounceClass"
    />
    <div
      class="bounce3"
      v-bind:class="bounceClass"
    />
  </div>
</template>

<script setup lang="ts">
defineProps({
  bounceClass: {
    type: String,
    default: 'bg-orange'
  }
});
</script>

<style scoped lang="scss">
.spinner {
  left: 50%;
  opacity: 1;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.3s ease-out 1.5s;

  &.hidden {
    opacity: 0;
  }
}

.spinner .bounce1 {
  animation-delay: -0.32s;
}

.spinner .bounce2 {
  animation-delay: -0.16s;
}

@keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

@keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

.spinner > div {
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  border-radius: 100%;
  display: inline-block;
  height: 12px;
  width: 12px;
}
</style>
