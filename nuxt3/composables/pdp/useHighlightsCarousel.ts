import { omit } from 'lodash-es';

type StoryData = {
  url: string,
  duration: number,
  title?: string,
  description?: string,
  type: 'video' | 'image',
  theme: 'dark' | null,
  videoId?: { mobile: string, desktop: string }
}
type VideoCardData = {
  tagline?: string;
  headline?: string;
  theme?: 'dark';
  videoPath?: string;
  imagePath: string;
}
const SplitType = {
  byCategory: 'byCategory',
  byHeight: 'byHeight',
  none: null
} as const;
type SplitType = typeof SplitType[keyof typeof SplitType];
type HeightType = 'low' | 'high';
type Collection = 'AP' | 'AV' | 'B';

const getCategoryId = (category: FurnitureCategory): number | null => {
  const categoryPure = category.toLowerCase().replaceAll('_', '');
  const index = ['sideboard',
    'wallstorage',
    'bookcase',
    'tvstand',
    'chest',
    'shoerack',
    'bedsidetable',
    'vinylstorage',
    'desk',
    'dressingtable'].findIndex(item => item === categoryPure);

  return index === -1 ? null : index + 1;
};

const getCategoryHeightType = (category: FurnitureCategory): HeightType =>
  (['bookcase', 'wallstorage'].includes(category) ? 'high' : 'low');

const getCollectionCardObject = (
  lp: number,
  collection: Collection,
  category:FurnitureCategory | null = null,
  splitBy: SplitType = SplitType.none
): VideoCardData => {
  const heightType : HeightType | null = category
    ? getCategoryHeightType(category)
    : null;

  const categoryId : number | null = category ? getCategoryId(category) : null;
  let phraseBaseKey = `pdp.animation-highlights.${collection}.tile${lp}`;

  let pathSuffix = '';

  switch (splitBy) {
    case SplitType.byCategory:
      phraseBaseKey = `${phraseBaseKey}.${category}`;
      pathSuffix = `${categoryId}`;
      break;
    case SplitType.byHeight:
      phraseBaseKey = `${phraseBaseKey}.${heightType}`;
      pathSuffix = `${heightType === 'low' ? '' : '2'}`;
      break;
  }

  const assetPath = `pdp/section-highlights-carousel/${collection}/${lp}/${lp}${collection}${pathSuffix}/${lp}${collection}${pathSuffix}`;

  return {
    tagline: `${phraseBaseKey}.subheadline`,
    headline: `${phraseBaseKey}.headline`,
    theme: 'dark',
    videoPath: assetPath,
    imagePath: assetPath
  };
};

const getStoryData = (
  lp: number,
  storyLp: number,
  collection: Collection,
  category: FurnitureCategory | null = null,
  splitBy: SplitType = SplitType.none,
  type: 'video' | 'image' = 'image'
):StoryData => {
  const heightType : HeightType | null = category
    ? getCategoryHeightType(category)
    : null;
  const categoryId : number | null = category ? getCategoryId(category) : null;

  let phraseBaseKey = `pdp.animation-highlights.${collection}.tile${lp}.story${storyLp}`;

  let pathSuffix = `_POP${storyLp}`;

  switch (splitBy) {
    case SplitType.byCategory:
      phraseBaseKey = `${phraseBaseKey}.${category}`;
      pathSuffix = `${categoryId}_POP${storyLp}`;
      break;
    case SplitType.byHeight:
      phraseBaseKey = `${phraseBaseKey}.${heightType}`;
      pathSuffix = `${heightType === 'high' ? '' : '2'}_POP${storyLp}`;
      break;
  }

  const assetPath = `pdp/section-highlights-carousel/${collection}/${lp}/${lp}${collection}${pathSuffix}`;
  const videoPath = `pdp/section-highlights-carousel/${collection}/${lp}/${lp}${collection}${pathSuffix}`;

  return {
    url: assetPath,
    title: `${phraseBaseKey}.subheadline`,
    description: `${phraseBaseKey}.body`,
    duration: 10000,
    type,
    theme: 'dark',
    videoId: type === 'video'
      ? {
          mobile: `${videoPath}/M`,
          desktop: `${videoPath}/D`
        }
      : undefined
  };
};

const getADataCollection = (
  collection: Collection,
  category:FurnitureCategory
):Array<VideoCardData & { stories?: Array<StoryData> } > => {
  return [
    {
      ...getCollectionCardObject(1, collection, category, SplitType.none),
      stories: [
        getStoryData(1, 1, collection, category, SplitType.byHeight)
      ]
    },
    {
      ...omit(getCollectionCardObject(2, collection, category, SplitType.none), ['tagline', 'headline']),
      stories: [
        omit(getStoryData(2, 1, collection, category, SplitType.none), ['title', 'description']),
        getStoryData(2, 2, collection, category, SplitType.none)
      ]
    },
    {
      ...getCollectionCardObject(3, collection, category, SplitType.none),
      stories: [
        getStoryData(3, 1, collection, category, SplitType.none)
      ]
    },
    {
      ...getCollectionCardObject(4, collection, category, SplitType.byHeight),
      stories: [
        getStoryData(4, 1, collection, category, SplitType.byHeight)
      ]
    },
    {
      ...omit(getCollectionCardObject(5, collection, category, SplitType.none), ['tagline'])
    },
    {
      ...getCollectionCardObject(6, collection, category, SplitType.byCategory),
      stories: [
        getStoryData(6, 1, collection, category, SplitType.byCategory)
      ]
    },
    {
      ...getCollectionCardObject(7, collection, category, SplitType.none),
      stories: [
        getStoryData(7, 1, collection, category, SplitType.none, 'video'),
        getStoryData(7, 2, collection, category, SplitType.none)
      ]
    },
    {
      ...omit(getCollectionCardObject(8, collection, category, SplitType.none), ['videoPath'])
    }
  ];
};

export default (_shelfType:SHELF_TYPE, _categoryName : FurnitureCategory) => {
  const { t } = useI18n();
  const shelfType = ref(_shelfType);
  const categoryName = ref<FurnitureCategory>(_categoryName);
  const isShelfWithLegs = computed(() => shelfType.value === 8);

  const sofaData = () => [
    {
      tagline: t('pdp.sofa.modular.carousel.item1.title'),
      headline: t('pdp.sofa.modular.carousel.item1.subtitle'),
      theme: 'dark',
      ctaCopy: t('common.more'),
      imagePath: 'pdp/sofa/modular/1/A'
    },
    {
      tagline: t('pdp.sofa.modular.carousel.item2.title'),
      headline: t('pdp.sofa.modular.carousel.item2.subtitle'),
      theme: 'dark',
      ctaCopy: t('common.more'),
      imagePath: 'pdp/sofa/modular/2/A',
      videoPath: 'pdp/sofa/modular/2/A',
      stories: [
        {
          url: 'pdp/sofa/modular/2/popup',
          theme: 'dark',
          duration: 10000,
          title: t('pdp.sofa.modular.carousel.item2.popup.title'),
          description: t('pdp.sofa.modular.carousel.item2.popup.subtitle')
        }
      ]
    },
    {
      tagline: t('pdp.sofa.modular.carousel.item3.title'),
      headline: t('pdp.sofa.modular.carousel.item3.subtitle'),
      theme: 'dark',
      ctaCopy: t('common.more'),
      imagePath: 'pdp/sofa/modular/3/A',
      videoPath: 'pdp/sofa/modular/3/A'
    },
    {
      tagline: t('pdp.sofa.modular.carousel.item4.title'),
      headline: t('pdp.sofa.modular.carousel.item4.subtitle'),
      theme: 'dark',
      ctaCopy: t('common.more'),
      imagePath: 'pdp/sofa/modular/4/A',
      videoPath: 'pdp/sofa/modular/4/A'
    },
    {
      tagline: t('pdp.sofa.modular.carousel.item5.title'),
      headline: t('pdp.sofa.modular.carousel.item5.subtitle'),
      theme: 'dark',
      ctaCopy: t('common.more'),
      imagePath: 'pdp/sofa/modular/5/A',
      videoPath: 'pdp/sofa/modular/5/A'
    },
    {
      tagline: t('pdp.sofa.modular.carousel.item6.title'),
      headline: t('pdp.sofa.modular.carousel.item6.subtitle'),
      theme: 'dark',
      ctaCopy: t('common.more'),
      imagePath: 'pdp/sofa/modular/6/A',
      videoPath: 'pdp/sofa/modular/6/A',
      stories: [
        {
          url: 'pdp/sofa/modular/6/popup',
          theme: 'dark',
          duration: 10000,
          title: t('pdp.sofa.modular.carousel.item6.popup.title'),
          description: t('pdp.sofa.modular.carousel.item6.popup.subtitle')
        }
      ]
    },
    {
      tagline: t('pdp.sofa.modular.carousel.item7.title'),
      headline: t('pdp.sofa.modular.carousel.item7.title'),
      theme: 'dark',
      ctaCopy: t('common.more'),
      imagePath: 'pdp/sofa/modular/7/A',
      videoPath: 'pdp/sofa/modular/7/A',
      stories: [
        {
          url: 'pdp/sofa/modular/7/popup/1',
          theme: 'dark',
          duration: 10000,
          title: t('pdp.sofa.modular.carousel.item7.popup.title1'),
          description: t('pdp.sofa.modular.carousel.item7.popup.subtitle1')
        },
        {
          url: 'pdp/sofa/modular/7/popup/2',
          theme: 'dark',
          duration: 10000,
          title: t('pdp.sofa.modular.carousel.item7.popup.title2'),
          description: t('pdp.sofa.modular.carousel.item7.popup.subtitle2')
        }
      ]
    }
  ];

  const getExpressionData = () => [
    {
      tagline: t(`pdp.animation-highlights.tone-shelves.tile1.${isShelfWithLegs.value ? 't25' : 't23t24'}.subheadline`),
      headline: t(`pdp.animation-highlights.tone-shelves.tile1.${isShelfWithLegs.value ? 't25' : 't23t24'}.headline`),
      theme: 'dark',
      videoPath: isShelfWithLegs.value ? 'pdp/section-highlights-carousel/tone/video/1A_legs' : 'pdp/section-highlights-carousel/tone/video/1A',
      imagePath: `pdp/section-highlights-carousel/${isShelfWithLegs.value ? 'legs/2A' : 'floating/1A'}`,
      stories: [
        {
          url: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '1A_POP_legs' : '1A_POP'}`,
          duration: 10000,
          title: t(`pdp.animation-highlights.tone-shelves.tile1.${isShelfWithLegs.value ? 't25' : 't23t24'}.story1.headline`),
          description: t(`pdp.animation-highlights.tone-shelves.tile1.${isShelfWithLegs.value ? 't25' : 't23t24'}.story1.body`)
        }
      ]
    },
    {
      tagline: '',
      headline: '',
      theme: 'dark',
      ctaCopy: '',
      imagePath: `pdp/section-highlights-carousel/${isShelfWithLegs.value ? 'legs/2B' : 'floating/1B'}`
    },
    {
      tagline: t('pdp.animation-highlights.tone-shelves.tile3.subheadline'),
      headline: t('pdp.animation-highlights.tone-shelves.tile3.headline'),
      videoPath: isShelfWithLegs.value ? 'pdp/section-highlights-carousel/tone/video/1C_legs' : 'pdp/section-highlights-carousel/tone/video/1C',
      imagePath: `pdp/section-highlights-carousel/${isShelfWithLegs.value ? 'legs/2C' : 'floating/1C'}`,
      theme: 'dark',
      stories: [
      // C
        {
          url: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2C_POP1' : '1C_POP1'}`,
          duration: 10000,
          theme: 'dark',
          title: t('pdp.animation-highlights.tone-shelves.tile3.story1.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile3.story1.body')

        },
        {
          url: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2C_POP2' : '1C_POP2'}`,
          title: t('pdp.animation-highlights.tone-shelves.tile3.story2.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile3.story2.body'),
          type: 'video',
          theme: 'dark',
          videoId: {
            mobile: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2C_POP2' : '1C_POP2'}/M`,
            desktop: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2C_POP2' : '1C_POP2'}/D`
          }
        }
      ]
    },
    {
      // D
      tagline: '',
      headline: '',
      theme: 'dark',
      videoPath: 'pdp/section-highlights-carousel/tone/video/1D',
      imagePath: 'pdp/section-highlights-carousel/floating/1D',
      fitText: t('pdp.animation-highlights.tone-shelves.tile4.headline')
    },
    {
      // E
      tagline: t(`pdp.animation-highlights.tone-shelves.tile5.${categoryName.value}.subheadline`),
      headline: t(`pdp.animation-highlights.tone-shelves.tile5.${categoryName.value}.headline`),
      theme: 'dark',
      videoPath: `pdp/section-highlights-carousel/tone/video/1E_${categoryName.value}`,
      imagePath: `pdp/section-highlights-carousel/floating/1E_${categoryName.value}`,
      stories: [
      // E
      // TV-STAND
        (categoryName.value === 'tvstand' && {
          url: 'pdp/section-highlights-stories/1E1_POP1_tvstand',
          duration: 10000,
          theme: 'dark',
          title: t('pdp.animation-highlights.tone-shelves.tile5.tvstand.story1.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile5.tvstand.story1.body')
        }),
        (categoryName.value === 'tvstand' && {
          url: 'pdp/section-highlights-stories/1E1_POP2_tvstand',
          duration: 10000,
          theme: 'dark',
          title: t('pdp.animation-highlights.tone-shelves.tile5.tvstand.story2.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile5.tvstand.story2.body')
        }),
        // CHEST
        (categoryName.value === 'chest' && {
          url: 'pdp/section-highlights-stories/1E2_POP1_chest',
          duration: 10000,
          theme: 'dark',
          title: t('pdp.animation-highlights.tone-shelves.tile5.chest.story1.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile5.chest.story1.body')
        }),
        ((categoryName.value === 'chest') && {
          url: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2E2_POP2_chest' : '1E2_POP2_chest'}`,
          title: t('pdp.animation-highlights.tone-shelves.tile5.chest.story2.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile5.chest.story2.body'),
          type: 'video',
          theme: 'dark',
          videoId:
            {
              mobile: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2E2_POP2_chest' : '1E2_POP2_chest'}/M`,
              desktop: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2E2_POP2_chest' : '1E2_POP2_chest'}/D`
            }
        }),
        // BEDSIDE
        (categoryName.value === 'bedsidetable' && {
          url: 'pdp/section-highlights-stories/1E3_POP1_bedsidetable',
          duration: 10000,
          theme: 'dark',
          title: t('pdp.animation-highlights.tone-shelves.tile5.bedside_table.story1.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile5.bedside_table.story1.body')
        }),
        ((categoryName.value === 'bedsidetable') && {
          url: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2E3_POP2_bedside_table' : '1E3_POP2_bedsidetable'}`,
          title: t('pdp.animation-highlights.tone-shelves.tile5.bedside_table.story2.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile5.bedside_table.story2.body'),
          type: 'video',
          theme: 'dark',
          videoId:
            {
              mobile: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2E3_POP2_bedside_table' : '1E3_POP2_bedsidetable'}/M`,
              desktop: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2E3_POP2_bedside_table' : '1E3_POP2_bedsidetable'}/D`
            }
        }),
        // Sideboard
        (categoryName.value === 'sideboard' && {
          url: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2E4_POP_sideboard' : '1E4_POP_sideboard'}`,
          title: isShelfWithLegs.value ? t('pdp.animation-highlights.tone-shelves.tile1.t25.story1.headline') : t('pdp.animation-highlights.tone-shelves.tile5.sideboard.story1.headline'),
          description: isShelfWithLegs.value ? t('pdp.animation-highlights.tone-shelves.tile1.t25.story1.body') : t('pdp.animation-highlights.tone-shelves.tile5.sideboard.story1.body'),
          type: 'video',
          videoId:
            {
              mobile: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2E4_POP_sideboard' : '1E4_POP_sideboard'}/M`,
              desktop: `pdp/section-highlights-stories/${isShelfWithLegs.value ? '2E4_POP_sideboard' : '1E4_POP_sideboard'}/D`
            }
        })

      ].filter(Boolean)
    },
    {
      tagline: t('pdp.animation-highlights.tone-shelves.tile6.subheadline'),
      headline: t('pdp.animation-highlights.tone-shelves.tile6.headline'),
      theme: 'dark',
      videoPath: 'pdp/section-highlights-carousel/tone/video/1F',
      imagePath: 'pdp/section-highlights-carousel/floating/1F',
      stories: [
        shelfType.value === 6 && {
          url: 'pdp/section-highlights-stories/1F_POP0A_minimal',
          duration: 5000,
          title: t('pdp.animation-highlights.tone-shelves.tile6.story1.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile6.t23t25.story1.body'),
          type: 'video',
          theme: 'dark',
          videoId:
            {
              mobile: 'pdp/section-highlights-stories/1F_POP0A_minimal/M',
              desktop: 'pdp/section-highlights-stories/1F_POP0A_minimal/D'
            }
        },
        shelfType.value === 7 && {
          url: 'pdp/section-highlights-stories/1F_POP0B_floating',
          duration: 5000,
          title: t('pdp.animation-highlights.tone-shelves.tile6.story1.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile6.t24.story1.body'),
          type: 'video',
          theme: 'dark',
          videoId: {
            mobile: 'pdp/section-highlights-stories/1F_POP0B_floating/M',
            desktop: 'pdp/section-highlights-stories/1F_POP0B_floating/D'
          }
        },
        shelfType.value === 8 && {
          url: 'pdp/section-highlights-stories/2F_POP0C',
          duration: 5000,
          title: t('pdp.animation-highlights.tone-shelves.tile6.story1.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile6.t23t25.story1.body'),
          type: 'video',
          theme: 'dark',
          videoId: {
            mobile: 'pdp/section-highlights-stories/2F_POP0C/M',
            desktop: 'pdp/section-highlights-stories/2F_POP0C/D'
          }
        },
        {
          url: 'pdp/section-highlights-stories/1F_POP1',
          duration: 10000,
          type: 'image',
          theme: 'dark',
          title: t('pdp.animation-highlights.tone-shelves.tile6.story2.headline'),
          description: t('pdp.animation-highlights.tone-shelves.tile6.story2.body')
        }
      ].filter(Boolean)
    }
  ];
  const carouselContentData = computed(() => {
    switch (shelfType.value) {
      case 0:
        return getADataCollection('AP', _categoryName);
        break;
      case 1:
        return getADataCollection('B', _categoryName);
        break;
      case 2:
        return getADataCollection('AV', _categoryName);
        break;
      case 6: case 7: case 8:
        return getExpressionData();
        break;
      case 10:
        return sofaData();
        break;
      default:
        return null;
    }
  });

  return {
    shelfType,
    categoryName,
    carouselContentData
  };
};
