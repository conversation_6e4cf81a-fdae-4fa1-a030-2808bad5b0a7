import { type RegionName, regions } from '~/consts/regions';
import { switchRegion } from '~/api/region';

export default () => {
  const i18n = useI18n();
  const switchLocalePath = useSwitchLocalePath();
  const store = useGlobal();
  const { createLangCode, locale } = useLocale();
  const modalStore = useModalStore();
  const { $logException } = useNuxtApp();

  const changeRegion = async (regionName: RegionName) => {
    const { data, error } = await switchRegion(regionName);

    if (error.value) {
      $logException(error.value);
    } else {
      if (!data.value?.language) {
        $logException(new Error(`useRegion.ts: No language in response: ${data.value}`));
      }

      const newLocale = createLangCode(data.value?.language, regions[regionName].iso2);

      window.location.replace(switchLocalePath(newLocale) || `/${newLocale}`);
    }
  };

  const changeRegionWithoutReloading = (regionName: RegionName) => switchRegion(regionName);

  const onRegionChange = async (regionName: RegionName) => {
    const region = regions[regionName];

    if (store.hasT03 && region && !region.isT03Available) {
      modalStore.SET_REGION_NAME(regionName);
      modalStore.OPEN_REGION_MODAL();
    } else if (store.hasS01 && region && !region.s01Available) {
      modalStore.SET_REGION_NAME(regionName);
      modalStore.OPEN_REGION_MODAL_S01();
    } else if (store.hasCorduroy && region && !region.corduroyAvailable) {
      modalStore.SET_REGION_NAME(regionName);
      modalStore.OPEN_REGION_MODAL_CORDUROY();
    } else {
      await changeRegion(regionName);
    }
  };

  const translatedRegions = Object.values(regions).map(region => ({
    ...region,
    translatedName: i18n.t(region.nameKey)
  }));

  const translatedRegion = (regionName: RegionName) => {
    const region = regions[regionName]?.nameKey || 'loading';

    return i18n.t(region);
  };

  // @ts-ignore
  const collator = new Intl.Collator(locale.value);
  const sortedTranslatedRegions = translatedRegions.sort((a, b) => collator.compare(a.translatedName, b.translatedName));
  const otherRegionIndex = sortedTranslatedRegions.findIndex(region => region.name === '_other');

  const sortedTranslatedRegionsWithOtherRegion = [
    ...sortedTranslatedRegions.slice(0, otherRegionIndex),
    ...sortedTranslatedRegions.slice(otherRegionIndex + 1),
    ...sortedTranslatedRegions.slice(otherRegionIndex, otherRegionIndex + 1)
  ];
  const regionsForSelect = sortedTranslatedRegions
    .filter(region => region.name !== '_other')
    .map(region => ({ label: region.translatedName, value: region.name }));

  const regionsForSelectWithOtherRegion = sortedTranslatedRegions
    .map(region => ({ label: region.translatedName, value: region.name }))
    .sort((a, b) => (a.value === '_other' ? 1 : b.value === '_other' ? -1 : 0));

  const regionsForPhonePrefix = sortedTranslatedRegions
    .filter(region => region.name !== '_other')
    .map(region => region.iso2);

  return {
    changeRegion,
    onRegionChange,
    changeRegionWithoutReloading,
    translatedRegion,
    regionsForSelect,
    regionsForSelectWithOtherRegion,
    sortedTranslatedRegionsWithOtherRegion,
    sortedTranslatedRegions,
    regionsForPhonePrefix
  };
};
