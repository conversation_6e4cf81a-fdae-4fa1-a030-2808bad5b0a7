<template>
  <main>
    <Head>
      <Title>{{ $t('lp.press.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        v-bind:content="$t('lp.press.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        v-bind:content="$t('lp.press.meta.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        v-bind:content="$t('lp.press.meta.description')"
      />
    </Head>
    <section
      data-section="press-header"
      class="grid-container pt-64 bg-white"
    >
      <h1
        class="bold-28 md:bold-54 lg:bold-72 text-offblack-700 text-center pb-24 md:pb-64"
        v-html="$t('lp.press.title')"
      />
    </section>
    <SectionTextImageColumns
      data-section="values-section"
      class="bg-[#FFFFFF]"
      v-bind="{
        title: $t('lp.press.story.title'),
        description: $t('lp.press.story.description'),
        imagePath: 'lp/press',
        isLightTheme: false,
        isReversed: true,
        isReversedOnMobile: true,
        titleClass: 'bold-20 lg:bold-24 xl:bold-32 text-offblack-700 mt-24 mb-16 md:mb-24 text-left',
        descriptionClass: 'normal-24 lg:normal-32 xl:normal-42 text-offblack-700 text-left'
      }"
    />
    <SectionDownloads />
  </main>
</template>
