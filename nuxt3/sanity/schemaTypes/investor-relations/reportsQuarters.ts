interface ReportsQuarters {
  name: string;
  type: string;
  title: string;
  fields: Array<{
    title: string;
    name: string;
    type: string;
    validation?: (Rule: any) => any;
    of?: Array<{   
      type: 'reportFile';
    }>;
  }>;
}

const reportsQuarters: ReportsQuarters = {
  name: 'reportsQuarters',
  type: 'document',
  title: 'Investor relations reports',
  fields: [
    {
      title: 'Date',
      name: 'date',
      type: 'datetime',
      validation: Rule => Rule.required()
    },
    {
      name: 'title',
      type: 'string',
      title: 'Title',
      validation: Rule => Rule.required()
    },
    {
      name: 'files',
      type: 'array',
      title: 'Reports',
      of: [
        {type: 'reportFile'}
      ]
    }
  ]
};

export default reportsQuarters;
