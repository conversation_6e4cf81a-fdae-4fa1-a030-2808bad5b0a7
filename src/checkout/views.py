import logging

from decimal import Decimal
from typing import Optional

from django.conf import settings
from django.db.models import prefetch_related_objects
from django.utils.translation import gettext_lazy as _

from djangorestframework_camel_case.parser import Camel<PERSON>aseJ<PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON>J<PERSON><PERSON><PERSON><PERSON>
from rest_framework import (
    status,
    viewsets,
)
from rest_framework.decorators import action as drf_action
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from carts.choices import CartStatusChoices
from carts.models import Cart
from carts.services.cart_service import CartService
from checkout.exceptions import LackingTransactionError
from checkout.serializers import (
    CheckoutCartStatusSerializer,
    CheckoutOrderStatusSerializer,
    FreeOrderSerializer,
)
from checkout.services.region_restrictions import RegionRestrictionService
from checkout.validators import CheckoutValidator
from checkout.vat.validation import (
    sanitize_vat,
    vat_validator,
)
from custom.utils.url import get_request_source
from events.choices import BrazeSubscriptionStates
from events.domain_events.marketing_events import (
    CheckoutEntryEvent,
    CheckoutFulfilledEvent,
    UserContactDataUpdateEvent,
)
from events.tasks import (
    subscribe_email_event,
    subscribe_user_to_sms_group,
)
from orders.enums import OrderStatus
from orders.models import Order
from orders.serializers import OrderAddressFormSerializer
from payments.models import Transaction
from payments.services.free_order_handler import FreeOrderHandler
from pricing_v3.services.price_calculators import OrderPriceCalculator
from user_profile.choices import SubscriptionSources
from user_profile.models import (
    AddressDataAbstractModel,
    UserProfile,
)
from user_profile.serializers import CheckoutInitialFormSerializer

logger = logging.getLogger('cstm')


class CheckoutCartApiViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    renderer_classes = [CamelCaseJSONRenderer]
    parser_classes = [CamelCaseJSONParser]

    def get_queryset(self):
        return (
            Cart.objects.filter(
                owner=self.request.user,
                status__in=[CartStatusChoices.ACTIVE, CartStatusChoices.DRAFT],
            )
            .select_related('order', 'owner__profile')
            .prefetch_related('order__transactions')
        )

    @drf_action(detail=True, methods=['get'])
    def initial_form_data(self, request, *args, **kwargs):
        cart = self.get_object()
        serializer = CheckoutInitialFormSerializer(
            cart.owner.profile,
            context={'request': request, 'order': cart.order},
        )
        return Response(serializer.data)

    @drf_action(detail=True, methods=['patch'])
    def sync_with_order(self, request, *args, **kwargs):
        """
        Update order pricing fields based on the one from the cart
        and order address fields based on the one from the request payload

        Create order if it does not exist and set order address fields
        based on the one from the user profile data

        Subscribe user to newsletter and sms if requested emitting correct events
        """
        cart = self.get_object()
        cart_service = CartService(cart)

        if not cart_service.order_exists():
            self._update_request_data(data=request.data, profile=cart.owner.profile)

        new_vat_number = request.data.get('vat')
        if new_vat_number is not None:
            validated_vat_number = self._validate_vat_number(
                cart=cart,
                vat_number=new_vat_number,
                invoice_country=request.data.get('invoice_country'),
            )
            request.data['vat'] = validated_vat_number
            vat_validated = vat_validator(validated_vat_number)
        else:
            vat_validated = False

        order = cart_service.sync_with_order(
            request=request,
            source=get_request_source(request),
        )
        serializer = OrderAddressFormSerializer(
            instance=order,
            data=request.data,
        )
        serializer.is_valid(raise_exception=True)
        order = serializer.save()

        # TODO: hotfix, remove later
        OrderPriceCalculator(order).calculate(check_vat=True)

        data = serializer.validated_data
        if request.data.get('newsletter'):
            self._subscribe_to_newsletter(data)
        if request.data.get('sms'):
            self._subscribe_to_sms(data)

        return Response(
            {'order_id': order.id, 'cart_id': cart.id, 'vat_validated': vat_validated}
        )

    @drf_action(detail=True)
    def status(self, request, *args, **kwargs):
        return Response(
            CheckoutCartStatusSerializer(self.get_object()).data,
            status=status.HTTP_200_OK,
        )

    @drf_action(detail=True, methods=['post'])
    def trigger_checkout_entry_event(self, *args, **kwargs):
        cart = self.get_object()
        CheckoutEntryEvent(user=self.request.user, cart_id=cart.id)
        return Response('Event was emitted', status=status.HTTP_200_OK)

    @drf_action(detail=True, methods=['post'])
    def trigger_checkout_fulfilled_event(self, *args, **kwargs):
        cart = self.get_object()
        profile = cart.owner.profile
        CheckoutFulfilledEvent(user=self.request.user, order_id=cart.order_id)
        UserContactDataUpdateEvent(
            user=self.request.user,
            phone_number=profile.phone,
            phone_prefix=profile.phone_prefix,
            first_name=profile.first_name,
            last_name=profile.last_name,
            postal_code=profile.postal_code,
        )
        return Response('Events were emitted', status=status.HTTP_200_OK)

    # TODO: Remove this view
    @drf_action(detail=True, methods=['post'])
    def validate_vat_number(self, request, *args, **kwargs):
        cart = self.get_object()
        cart_country = cart.region.get_country(without_cache=True)
        if 'vat_number' not in request.data:
            return Response(
                'VAT number is required',
                status=status.HTTP_400_BAD_REQUEST,
            )
        vat_number = request.data.get('vat_number')
        invoice_country_name = request.data.get('invoice_country', cart_country.name)

        if not vat_number:
            self._set_vat_and_recalculate_cart(cart, '', invoice_country_name)
            return Response({'validated': False})

        sanitized_vat = sanitize_vat(vat_number, country_name=invoice_country_name)
        self._set_vat_and_recalculate_cart(cart, sanitized_vat, invoice_country_name)

        return Response({'validated': vat_validator(sanitized_vat)})

    @drf_action(detail=True, methods=['patch'])
    def set_recycle_tax_value(self, request, *args, **kwargs):
        cart = self.get_object()
        prefetch_related_objects([cart], 'items__cart_item')

        total_recycle_tax_value = Decimal('0')
        items = []

        for cart_item in cart.items.all():
            recycle_tax_value = cart_item.sellable_item.get_recycle_tax_value(
                cart_or_order=cart,
            )
            total_recycle_tax_value += Decimal(recycle_tax_value)
            cart_item.recycle_tax_value = recycle_tax_value
            items.append(cart_item)

        cart.items.bulk_update(items, fields=['recycle_tax_value'])

        return Response(
            {
                'total_price': cart.total_price,
                'total_recycle_tax_value': total_recycle_tax_value,
                'total_price_without_recycle_tax': cart.total_price
                - total_recycle_tax_value,
            }
        )

    @staticmethod
    def _validate_vat_number(
        cart: Cart, vat_number: str, invoice_country: Optional[str]
    ) -> str:
        sanitized_vat = (
            sanitize_vat(vat_number, country_name=invoice_country)
            if vat_number
            else vat_number
        )

        needs_update = False
        if cart.vat != sanitized_vat:
            cart.vat = sanitized_vat
            needs_update = True

        if cart.invoice_country != invoice_country:
            cart.invoice_country = invoice_country
            needs_update = True

        if needs_update:
            cart.save(update_fields=['vat', 'invoice_country'])
            CartService.recalculate_cart(cart, check_vat=True)

        return sanitized_vat

    # TODO: Remove this method
    @staticmethod
    def _set_vat_and_recalculate_cart(
        cart: Cart, vat_number: Optional[str], invoice_country: str
    ) -> None:
        cart.vat = vat_number
        cart.invoice_country = invoice_country
        cart.save(update_fields=['vat', 'invoice_country'])
        CartService.recalculate_cart(cart, check_vat=True)

    def _subscribe_to_newsletter(self, data: dict) -> None:
        email = data.get('email')
        subscribe_email_event.delay(
            self.request.user.id,
            email=email,
            subscription_data={
                'group': settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['newsletter'],
                'group_state': BrazeSubscriptionStates.SUBSCRIBED,
                'email': email,
                'source': SubscriptionSources.CHECKOUT,
                'global_state': BrazeSubscriptionStates.OPTED_IN,
            },
        )

        self.request.user.profile.newsletter_agreed = True
        self.request.user.profile.save(update_fields=['newsletter_agreed'])

    def _subscribe_to_sms(self, data: dict) -> None:
        subscribe_user_to_sms_group.delay(
            user_id=self.request.user.id,
            email=data.get('email'),
            subscription_data={
                'source': SubscriptionSources.CHECKOUT,
                'phone_prefix': data.get('phone_prefix'),
                'phone_number': data.get('phone_number'),
                'group_state': BrazeSubscriptionStates.SUBSCRIBED,
            },
        )

        self.request.user.profile.sms_agreed = True
        self.request.user.profile.save(update_fields=['sms_agreed'])

    def _update_request_data(self, data: dict, profile) -> None:
        has_different_invoice_data = profile.different_billing_address
        for address_field in AddressDataAbstractModel.ADDRESS_FIELDS:
            if address_field == 'country_area':
                continue
            if address_field == 'vat' and not has_different_invoice_data:
                continue
            self._set_order_address_field(data, profile, address_field)

        for invoice_address_field in AddressDataAbstractModel.INVOICE_ADDRESS_FIELDS:
            if invoice_address_field in {'invoice_country_area', 'invoice_vat'}:
                continue
            self._set_order_address_field(
                data,
                profile,
                invoice_address_field,
                use_invoice_value=has_different_invoice_data,
            )

    @staticmethod
    def _set_order_address_field(
        data: dict, profile: UserProfile, key: str, use_invoice_value: bool = False
    ) -> None:
        """
        data['city'] = profile.city
        if use_invoice_address:
            data['invoice_city'] = profile.invoice_city
        else:
            data['invoice_city'] = profile.city
        """

        if use_invoice_value:
            value = getattr(profile, key, None)
        else:
            value = getattr(profile, key.replace('invoice_', ''), None)
        if key not in data and value:
            data[key] = value


class CheckoutOrderApiViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated]
    renderer_classes = [CamelCaseJSONRenderer]
    parser_classes = [CamelCaseJSONParser]

    def get_queryset(self):
        return (
            Order.objects.filter(
                owner=self.request.user,
                status__in=[
                    OrderStatus.DRAFT,
                    OrderStatus.CART,
                    OrderStatus.PAYMENT_FAILED,
                    OrderStatus.PAYMENT_PENDING,
                ],
            )
            .select_related('owner__profile', 'cart')
            .prefetch_related('transactions')
        )

    @drf_action(detail=True)
    def status(self, request, *args, **kwargs):
        return Response(
            CheckoutOrderStatusSerializer(self.get_object()).data,
            status=status.HTTP_200_OK,
        )

    @drf_action(detail=True)
    def is_free(self, request, *args, **kwargs):
        return Response(
            FreeOrderSerializer(self.get_object()).data,
            status=status.HTTP_200_OK,
        )

    @drf_action(detail=True, methods=['post'])
    def handle_free_order(self, *args, **kwargs):
        order = self.get_object()
        if not order.is_free():
            return Response(
                data='Order is not free',
                status=status.HTTP_400_BAD_REQUEST,
            )

        transaction = Transaction.objects.create(
            order=order,
            merchant_reference=order.adyen_reference,
            amount=0,
        )
        FreeOrderHandler(transaction, self.request).run()
        return Response(data={'order_id': order.id}, status=status.HTTP_200_OK)

    @drf_action(detail=True, methods=['get'])
    def initial_form_data(self, request, *args, **kwargs):
        order = self.get_object()
        serializer = CheckoutInitialFormSerializer(
            order.owner.profile,
            context={'request': request, 'order': order},
        )
        return Response(serializer.data)

    @drf_action(detail=True, methods=['post'])
    def validate_order_before_payment(self, request, *args, **kwargs):
        try:
            CheckoutValidator(self.get_object()).validate()
        except ValidationError as e:
            return Response(
                data={
                    'validated': False,
                    'message': str(e.detail[0]),
                },
                status=status.HTTP_200_OK,
            )
        except LackingTransactionError as e:
            return Response(
                data={
                    'message': str(e),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        return Response(
            data={
                'validated': True,
                'message': 'OK',
            },
            status=status.HTTP_200_OK,
        )


class PostalCodeValidationApiView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        postal_code = request.query_params.get('postal_code', None)
        cart_id = request.query_params.get('cart_id', None)
        try:
            cart = Cart.objects.select_related('region').get(id=cart_id)
        except Cart.DoesNotExist:
            return Response(
                _('Cart does not exist'), status=status.HTTP_400_BAD_REQUEST
            )

        region_restriction_service = RegionRestrictionService(
            postal_code=postal_code, region=cart.region
        )
        if region_restriction_service.is_outside_of_tax_union():
            return Response({'valid': False, 'reason': 'outside of tax union'})
        cart_service = CartService(cart=cart)
        if (
            cart_service.has_s01
            and region_restriction_service.is_sofa_delivery_unavailable()
        ):
            return Response({'valid': False, 'reason': 'outside of sofa delivery area'})

        return Response({'valid': True})
