# Generated by Django 4.1.9 on 2024-03-01 10:52

import django.core.files.storage
import django.core.validators

from django.db import (
    migrations,
    models,
)

import complaints.models
import complaints.utils


class Migration(migrations.Migration):

    dependencies = [
        ('complaints', '0046_complaint_exported_to_big_query'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='complaintimage',
            name='owner',
        ),
        migrations.AlterField(
            model_name='complaintimage',
            name='image',
            field=models.ImageField(
                max_length=150,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to=complaints.models.complaint_file_name,
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=['jpg', 'jpeg', 'png', 'webp']
                    ),
                    complaints.utils.validate_image_size,
                ],
            ),
        ),
    ]
