from django.db import models

from cstm_be.media_storage import private_media_storage
from custom_audiences.enums import CustomAudienceBatchStatus


class CustomAudienceBatch(models.Model):
    name = models.CharField(max_length=512)
    created_at = models.DateTimeField(auto_now_add=True)
    csv_file = models.FileField(
        upload_to='custom_audiences/custom_audience_batch/%Y/%m',
        storage=private_media_storage,
        null=True,
        blank=True,
        max_length=150,
    )
    archived_at = models.DateTimeField(null=True)
    filters = models.JSONField(null=True)

    class Meta:
        permissions = (('wisely_general', 'Permission for Wisely general API access.'),)
        verbose_name = 'Custom Audience Batch'
        verbose_name_plural = 'Custom Audience Batch'

    @property
    def status(self):
        if self.archived_at:
            return CustomAudienceBatchStatus.ARCHIVED.value
        if self.csv_file:
            return CustomAudienceBatchStatus.READY.value
        return CustomAudienceBatchStatus.PENDING.value

    def archive(self):
        if self.csv_file:
            self.csv_file.delete()
