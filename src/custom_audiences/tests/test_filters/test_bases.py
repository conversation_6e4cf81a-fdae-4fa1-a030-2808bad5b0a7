from django.contrib.auth import get_user_model

import pytest

from django_filters import filters

from custom_audiences.filters.bases import (
    BasePythonFilter,
    PythonFilterSet,
)
from user_profile.tests.factories import UserFactory

User = get_user_model()


def dummy_filter(instance, value):
    """Dummy filter function for testing purposes."""
    return instance % value == 0


def is_facebook_user(instance, value):
    """Dummy filter function for testing purposes."""
    has_fb_id = bool(instance.profile.facebook_id)
    return has_fb_id == value


class BooleanPythonFilter(BasePythonFilter, filters.BooleanFilter):
    """``BasePythonFilter`` just to use in tests."""


class DummyUserPythonFilterSet(PythonFilterSet):
    """``PythonFilterSet`` just to use in tests."""

    is_staff = filters.BooleanFilter()
    is_facebook_user = BooleanPythonFilter(filter_func=is_facebook_user)

    class Meta:
        model = User
        fields = ('is_staff', 'is_facebook_user')


class TestBasePythonFilter:
    @pytest.mark.parametrize(
        ('exclude', 'instance', 'value', 'expected_result'),
        [
            (True, 1, 2, True),
            (False, 1, 2, False),
        ],
    )
    def test_get_method(self, exclude, instance, value, expected_result):
        """Ensure expected filter method is returned."""
        python_filter = BasePythonFilter(
            filter_func=dummy_filter,
            exclude=exclude,
        )
        filter_method = python_filter.get_method(None)
        assert filter_method(instance, value) is expected_result

    def test_filter(self):
        """Ensure expected instances are yielded."""
        python_filter = BasePythonFilter(filter_func=dummy_filter)
        assert list(python_filter.filter([1, 2, 3, 4], value=2)) == [2, 4]


class TestPythonFilterSet:
    def test_python_filters(self):
        """Ensure all python filter names are returned."""
        filter_set = DummyUserPythonFilterSet()
        assert filter_set.python_filters == {
            'is_facebook_user',
        }

    @pytest.mark.django_db
    def test_filter_queryset(self):
        """Ensure expected data is returned when both types of filters used."""
        users = [
            UserFactory.create(is_staff=False, profile__facebook_id=None),
            UserFactory.create(is_staff=False, profile__facebook_id=1234),
            UserFactory.create(is_staff=True, profile__facebook_id=None),
            UserFactory.create(is_staff=True, profile__facebook_id=1344),
        ]
        filter_set = DummyUserPythonFilterSet(
            data={'is_staff': True, 'is_facebook_user': True},
            queryset=User.objects.all(),
        )
        assert list(filter_set.qs) == [users[-1]]
