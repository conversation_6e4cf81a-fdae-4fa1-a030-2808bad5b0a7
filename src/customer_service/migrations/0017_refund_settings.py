# Generated by Django 3.2.16 on 2022-10-20 13:09

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import customer_service.enums


class Migration(migrations.Migration):

    dependencies = [
        ('regions', '0005_add_sku_and_fast_track_to_region'),
        ('orders', '0029_extend_order'),
        ('customer_service', '0016_alter_csuserprofile_user_type'),
    ]

    operations = [
        migrations.DeleteModel(
            name='RefundInfo',
        ),
        migrations.CreateModel(
            name='MentionMeFile',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'file',
                    models.FileField(
                        blank=True, null=True, upload_to='logistic/mention_me/%Y/%m'
                    ),
                ),
                ('email_receive_date', models.DateTimeField(auto_now_add=True)),
                ('parsed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='RefundInfo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('referer_email', models.EmailField(blank=True, max_length=254)),
                ('referee_email', models.EmailField(blank=True, max_length=254)),
                (
                    'status',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'CORRECT_ORDER_NOT_FOUND'),
                            (2, 'WAITING'),
                            (3, 'READY_TO_REFUND'),
                            (4, 'REFUNDED'),
                        ],
                        default=1,
                    ),
                ),
                (
                    'order_info',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'NOT_MATCHED_ORDER'),
                            (2, 'NOT_DELIVERED_YET'),
                            (3, 'RETURN'),
                            (4, 'NOT_ENOUGH_DAYS_FROM_DELIVERY'),
                            (5, 'ONLY_SAMPLES'),
                            (6, 'TO_MANY_REFUNDS'),
                            (7, 'TO_HIGH_REFUNDS'),
                            (8, 'INCORRECT_REFERER_ORDER'),
                            (9, 'OK'),
                        ],
                        default=customer_service.enums.RefereeOrderInfo[
                            'NOT_MATCHED_ORDER'
                        ],
                    ),
                ),
                ('amount', models.IntegerField(default=0)),
                ('currency_code', models.CharField(default='EUR', max_length=5)),
                ('created_at', models.DateTimeField(auto_now=True)),
                (
                    'origin_file',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='customer_service.mentionmefile',
                    ),
                ),
                (
                    'referee_order',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='referee_info',
                        to='orders.order',
                    ),
                ),
                (
                    'referer_order',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='referer_info',
                        to='orders.order',
                    ),
                ),
            ],
            options={
                'unique_together': {('referee_email', 'referer_email')},
            },
        ),
        migrations.CreateModel(
            name='RefundSetting',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('min_order_value', models.PositiveIntegerField(default=100)),
                (
                    'currency',
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='regions.currency',
                    ),
                ),
            ],
        ),
    ]
