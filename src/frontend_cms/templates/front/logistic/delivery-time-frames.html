{% extends "front/base-logistic.html" %}
{% load i18n %}
{% load static %}
{% load user_agents %}
{% load components %}
{% load webpack_loader %}

{% block title %}{% trans "metatags_faq_title_1" %}{% endblock %}
{% block meta_title %}{% trans "metatags_faq_title_1" %}{% endblock %}
{% block description %}{% trans "metatags_faq_description_1" %}{% endblock %}

{% block extraheadfirst %}
    <link rel="stylesheet" href="{% static "dist/css/delivery-time-frames.css" %}">
{% endblock %}

{% block content %}
    {% trans 'delivery_timeslots_header' as copy %}
    {% include "components/sp-2018/_sp-2018-jumbo.html" with copy=copy %}

    <section id="vue-delivery-time-frames"></section>
    <script type="text/javascript">

        'use strict';

        $(document).ready(() => {setTimeout(() => {
            $('.dixa-messenger-namespace').addClass('always-visible')}, 2000);
        })
        window.LOGISTIC_URL = "{{ LOGISTIC_URL }}"
        window.USER_TOKEN = "{{ USER_TOKEN }}"

        window.order_details = {{ order_details | safe }}
        window.dtf_id = "{{ dtf_id | safe }}"
        window.username = "{{ username | safe }}"
        window.last_date_for_change = {{ last_date_for_change | safe}}
        window.accepted_timeslot = {
            date: "{{ accepted_timeslot_date | safe }}",
            startHour: "{{ accepted_timeslot_start_hour | safe }}",
            endHour: "{{ accepted_timeslot_end_hour | safe}}",
            allDay: {{ accepted_timeslot_all_day | safe}}
        }
    </script>
    {% if request|is_ie %}
        <!-- ie! -->
        {% render_bundle 'vueDeliveryTimeFrames' config='ES5' %}
    {% else %}
        {% render_bundle 'vueDeliveryTimeFrames' %}
    {% endif %}
{% endblock %}

