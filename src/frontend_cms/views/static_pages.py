from django.views.generic import TemplateView


class ReverseOnlyView(TemplateView):
    """View that renders a 404 template.

    This view is used for old Django views that have been migrated to Nuxt
    and no longer have templates. However, the routing for these views is still kept in
    the URLconf for the purpose of reversing URLs.

    To prevent errors like TemplateDoesNotExist when this view is accessed,
    such as through sentry (TYLKO-1JRY), a 404 template is rendered instead.
    """

    template_name = '404.html'
