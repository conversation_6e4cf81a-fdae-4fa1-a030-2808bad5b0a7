export const supportWidth = 125;
export const supportThicknessT01 = 12;
export const supportThicknessT02 = 18;
export const geometryFields = [
    'verticals',
    'horizontals',
    'doors',
    'backs',
    'drawers',
    'supports',
    'modules',
    'legs',
    'inserts',
    'plinth',
    'long_legs',
    'cable_management',
    'desk_beams',
];
export const elementTypeByCompartmentField = {
  backs: 'backs',
  drawers: 'drawers',
  doors: 'doors',
  horizontalInserts: 'inserts'
};
export const rowHeights = {
  A: 182,
  B: 282,
  C: 382,
  D: 482,
  E: 582,
  F: 682,
  G: 782,
}
export const allowedDrawerDepths = [
    320,
    400,
    500,
]
export const legOffsets = {
  0: 20,
  1: 25,
  2: 25,
  'depth_240': 20,
};
export const maxHorizontalLength = 2400;
export const maxBackWidth = 553;
export const longLegOffset = 9;
export const longLegHeight = 100;
export const minDoorWidth = 190;
export const insertsFrontOffset = 24;
export const insertsDepthOffset = {
  'standard': insertsFrontOffset,
  'full-depth': 0,
}
