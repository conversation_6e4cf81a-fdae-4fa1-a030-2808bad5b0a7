{% extends 'mails/_base_templates/base_flow.html' %}
{% load i18n static mailing_tags %}

{% block content %}
    {% trans 'mail_complaint_service_choose_date_preheader' as header_1 %}
    {% trans 'mail_complaint_service_new_date_request_cta' as button_text %}

    {% include 'mails/_components/transaction-header.html' with utm='utm_campaign=transaction_order_placed' header=header_1 paragraph='' %}

    {% mail_paragraph '' join='lower' margin_bottom=0 font_size=17 font_height=24 paragraph_color='#7c7d81' paragraph_align='left'%}
    {% blocktrans with user=user_name %}mail_complaint_service_choose_date_welcome_{{ user }}{% endblocktrans %}
    <br><br>
    {% trans 'mail_complaint_service_choose_date_paragraph_1_1' %}
    <br><br>
    {% for proposal in proposals %}
        {% with date=proposal.date|date:"d/m/Y" from_hour=proposal.from_hour|time:"H:i" to_hour=proposal.to_hour|time:"H:i" %}
            {% blocktrans asvar button_text with date=proposal.date|date:"d/m/Y" from_hour=proposal.from_hour|time:"H:i" to_hour=proposal.to_hour|time:"H:i" %}mail_complaint_service_question_for_many_dates_paragraph_2_2_{{ date }}_{{ from_hour }}_{{ to_hour }}{% endblocktrans %}
            {% mail_button button_text url='complaint_service_accept_date_proposal' date_proposal_id=proposal.id button_align='center' margin_bottom=15 %}
        {% endwith %}
    {% endfor %}
    <br>
    {% blocktrans with duration=duration %}mail_complaint_service_choose_date_paragraph_2_1_{{ duration }}{% endblocktrans %}
    <br><br>
    {% blocktrans with date=expiration_date|date:"d/m/Y" time=expiration_time|time:"H:i" %}mail_complaint_service_choose_date_paragraph_2_2_{{ date }}_{{ time }}{% endblocktrans %}
    <br><br>
    {% mail_button button_text url='complaint_service_new_date_request' service_id=service_id button_align='center' %}
    {% trans 'mail_complaint_service_choose_date_paragraph_2_3' %}
    <br><br>
    {% trans 'mail_complaint_service_choose_date_signature' %}
{% endblock %}

{% block unsubscribe %}
    {% include 'mails/_base_templates/_base_includes/_unsubscribe.html' %}
{% endblock %}
