import datetime

from django.core.management.base import BaseCommand

from model_transfers.data_managers import MarginTestProductsExportManager


class Command(BaseCommand):
    help = 'Creates fixture with simplified data.'

    def add_arguments(self, parser):
        parser.add_argument('--directory', type=str, default='fixtures/products')

    def handle(self, **options):
        now = datetime.datetime.now()

        for day in range(1, 30):
            MarginTestProductsExportManager(
                date_from=now - datetime.timedelta(days=3 * day),
                date_to=now - datetime.timedelta(days=3 * (day - 1)),
                directory=options['directory'],
                file_name=f'products{day}.json',
            ).export()
