import logging

from django.core.management.base import BaseCommand

from invoice.choices import InvoiceStatus
from invoice.models import Invoice

logger = logging.getLogger('orders')


class Command(BaseCommand):
    def handle(self, *args, **options):

        how_many = 0
        empty = []
        typical_vat_id = []
        ids = []
        typical_sell_rate_id = []
        # 2013 - du<PERSON>, z quantity, braku<PERSON> przy<PERSON>, korekta na tylko vat
        # 2978 - dwie faktury, 4 korekty, ostatnia dziwna
        # 2075 - eva krabbe, druga faktura
        # 998 - daftcode, 2 faktury, 1 proforma, dziwne korekty, faktury w zlotowkach
        # 2064 - drugi raz daftoce
        # 1988 - tikfoft
        # 2238 - westwing, rozbicie z quantity
        # 2265 - westwing, to samo
        # 2067 - tao, quantity totems

        ignore_invoices = [2013, 2978, 2075, 998, 2064, 1988, 2238, 2265, 2067]
        for i in Invoice.objects.filter(status=InvoiceStatus.CORRECTING):
            if i.id in ignore_invoices:
                continue
            if i.invoice_items.all().count() == 0:
                print(
                    f'Invoice {i.id}, {i.pretty_id[-10:]}, '
                    f'with reason {i.corrected_notes} has no items'
                )
                how_many += 1
                empty.append(i)
                continue
            if any([x.corrected_invoice_item is None for x in i.invoice_items.all()]):
                how_many += 1
                # "Wrong vat id/<br/>" in i.corrected_notes or "Wrong sell date,
                # exchange rate and vat id/<br/>" in i.corrected_notes or
                # "Wrong sell date and exchange rate/<br/>" in i.corrected_notes:
                typical_vat_id.append(i.pretty_id[-10:])
                ids.append(i)
                if (
                    i.invoice_items.all().count()
                    == i.corrected_invoice.invoice_items.all().count()
                ):
                    items = i.invoice_items.all().order_by('net_price')
                    previous_invoice = i.get_previous_correction()
                    if previous_invoice is None:
                        previous_invoice = i.corrected_invoice
                    corrected_items = previous_invoice.invoice_items.all().order_by(
                        'net_price'
                    )
                    for index, item in enumerate(items):
                        corrected_item = corrected_items[index]
                        item.corrected_invoice_item = corrected_item
                        item.vat_amount = corrected_item.vat_amount
                        item.vat_rate = corrected_item.vat_rate
                        item.gross_price = corrected_item.gross_price
                        item.save()
                continue
            # fixy na zepsute invoice item ids
            if i.get_previous_correction() is not None:
                previous_invoice_ids = [
                    (x.id, x.corrected_invoice_item_id)
                    for x in i.get_previous_correction().invoice_items.all()
                ]
                for invoice_item in i.invoice_items.all():
                    new_invoice_item_id = next(
                        (
                            x[0]
                            for x in previous_invoice_ids
                            if x[1] == invoice_item.corrected_invoice_item_id
                        ),
                        -1,
                    )
                    if new_invoice_item_id > -1:
                        invoice_item.corrected_invoice_item_id = new_invoice_item_id
                        invoice_item.save()
                        print(
                            f'Naprawilem item {invoice_item.id}, zmieniajac mu '
                            f'iinvoice_item.id na '
                            f'{invoice_item.corrected_invoice_item_id}'
                        )

        invoice_count = Invoice.objects.filter(status=InvoiceStatus.CORRECTING).count()
        print(f'For {invoice_count} we have {how_many} to fix')
        print(f'typical empty vat fix {len(typical_vat_id)}')
        print(f'typical empty vat + other fix {len(typical_sell_rate_id)}')
        print(f'empty {empty}')
        for x in ids:
            print(f'https://tylko.com/admin/invoice/invoice/{x.id}/')
            print(f'https://tylko.com/admin/invoice/invoice/?order_id={x.order_id}')
