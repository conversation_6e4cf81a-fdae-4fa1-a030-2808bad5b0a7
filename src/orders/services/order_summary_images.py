import io

from collections import defaultdict
from datetime import timedelta

from django.conf import settings
from django.core.files.storage import default_storage
from django.db.models import QuerySet
from django.utils import timezone

import requests

from PIL import (
    Image,
    ImageDraw,
    ImageFont,
)

from orders.models import OrderItem


def create_and_send_order_summary() -> None:
    qs = _get_order_items_from_yesterday()
    categories_map = defaultdict(list)
    for item in qs:
        categories_map[item.order_item.shelf_category].append(_create_image(item))

    for category_name, images in categories_map.items():
        category_image_url = _combine_images_for_category(images, category_name)
        _send_orders_summary_to_slack(category_name, len(images), category_image_url)


def _get_order_items_from_yesterday() -> QuerySet[OrderItem]:
    return (
        OrderItem.objects.filter(
            order__paid_at__date=timezone.now() - timedelta(days=1),
            order__parent_order__isnull=True,
        )
        .exclude(content_type__model='samplebox')
        .select_related('content_type')
        .prefetch_related('order_item')
    )


def _create_image(item: OrderItem) -> Image:
    furniture = item.order_item
    my_text = (
        f'{item.content_type.model} #{furniture.id}\n{item.price} EUR\n'
        f'{furniture.get_size()}'
    )
    img_temp = io.BytesIO(furniture.preview.read())
    with Image.open(img_temp) as img:
        img = img.resize((600, 600), Image.LANCZOS)
        draw = ImageDraw.Draw(img)

        font = ImageFont.load_default()
        draw.text((10, 10), my_text, fill='black', font=font)
        return img


def _combine_images_for_category(images: list[Image], category_name: str) -> str:
    height = images[0].height
    no_rows = len(images) // 3
    if len(images) % 3:
        no_rows += 1
    total_height = height * no_rows
    width = images[0].width

    with Image.new('RGB', (width * 3, total_height)) as combined:

        y_offset = 0
        x_offset = 0
        for img in images:
            combined.paste(img, (x_offset, y_offset))
            x_offset += width
            if x_offset > width * 2:
                x_offset = 0
                y_offset += img.height
        output = io.BytesIO()
        combined.save(output, format='JPEG', quality=50, optimize=True)
        output.seek(0)
        saved_img = default_storage.save(
            f'temp/sold_items/{category_name}_{timezone.now().timestamp()}.jpg',
            output,
        )
        return default_storage.url(saved_img)


def _send_orders_summary_to_slack(
    category_name: str,
    items_count: int,
    img_url: str,
) -> None:
    if not all([settings.SLACK_WEBHOOK, settings.IS_PRODUCTION]):
        return
    requests.post(
        settings.SLACK_WEBHOOK,
        json={
            'text': f'We sold {items_count} {category_name}s yesterday. {img_url}',
            'channel': 'sold_items_summary',
            'username': 'super-sprzedawca',
            'icon_emoji': ':money_with_wings:',
        },
    )
