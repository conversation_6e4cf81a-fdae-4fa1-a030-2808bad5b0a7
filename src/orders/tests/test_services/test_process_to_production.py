from datetime import timedelta
from decimal import Decimal
from unittest import mock

from django.db.models import Sum
from django.utils import timezone

import pytest

from events.models import Event
from orders.services.process_to_production import ProcessOrderToProduction


@pytest.mark.nbp
@pytest.mark.django_db
class TestProcessToProduction:
    @pytest.fixture(autouse=True)
    def set_up(self, mocker):
        self.mock_generated_pretty_id = mocker.patch(
            'invoice.models.Invoice._generate_pretty_id', return_value='test/1/2'
        )

    def test__get_or_create_invoice_should_create_invoice_when_no_klarna_payment_and_not_existing_invoice(
        self,
        order_factory,
        order_to_production_factory,
    ):
        order = order_factory(
            items=[], chosen_payment_method='Master Card', paid_at=timezone.now()
        )
        order_to_production = order_to_production_factory(original_order=order)
        ProcessOrderToProduction(
            order_to_production=order_to_production
        ).get_or_create_invoice()
        assert self.mock_generated_pretty_id.call_count == 1

    @mock.patch('orders.tasks.OrderToProductionValidator')
    def test_task_process_paid_order_to_production(
        self,
        mocked_order_to_production_validator,
        order_to_production_factory,
        order_factory,
    ):
        from orders.tasks import process_paid_order_to_production

        order = order_factory(
            items=[],
            chosen_payment_method='Master Card',
            paid_at=timezone.now() - timedelta(hours=10),
        )
        order_to_production = order_to_production_factory(
            original_order=order, is_klarna_payment=False
        )
        assert order_to_production.process_at is None
        process_paid_order_to_production()

        order_to_production.refresh_from_db()
        assert order_to_production.process_at is not None
        assert self.mock_generated_pretty_id.call_count == 1

    @mock.patch(
        'orders.services.process_to_production'
        '.MoveOrderToProduction.move_order_items_to_production'
    )
    @mock.patch('orders.internal_api.events.OrderPushedToProductionEvent.execute')
    @mock.patch('orders.internal_api.events.OrderStatusChangedEvent.execute')
    def test_emit_order_surface_calculation_event_when_order_moved_to_production(
        self,
        mocked_order_status_changed_event,
        mocked_pushed_to_production_event,
        _,
        order_to_production_factory,
        order_factory,
    ):
        order = order_factory(
            chosen_payment_method='Master Card',
            paid_at=timezone.now() - timedelta(hours=10),
        )
        order_to_production = order_to_production_factory(
            original_order=order,
            is_klarna_payment=False,
        )

        ProcessOrderToProduction(order_to_production).process()

        assert (
            Event.objects.filter(event_name='OrderSurfaceCalculationEvent').count() == 1
        )

    def test_calculate_recycle_tax_when_order_is_not_from_france(
        self,
        region_factory,
        order_to_production_factory,
    ):
        germany_region = region_factory(germany=True)
        order_to_production = order_to_production_factory(
            original_order__region=germany_region,
        )

        ProcessOrderToProduction(order_to_production).calculate_recycle_tax()
        items = order_to_production.original_order.items.aggregate(
            Sum('recycle_tax_value')
        )
        assert items['recycle_tax_value__sum'] == Decimal('0')

    @mock.patch(
        'orders.services.process_to_production.get_recycle_tax_value',
        return_value=Decimal('3.08'),
    )
    def test_calculate_recycle_tax_when_order_is_from_france(
        self,
        mocked_get_recycle_tax_value,
        region_factory,
        order_item_factory,
        product_factory,
        order_to_production_factory,
    ):
        france_region = region_factory(france=True)
        order_to_production = order_to_production_factory(
            original_order__region=france_region,
            original_order__items=[],
        )
        product_factory(
            order=order_to_production.original_order,
            order_item=order_item_factory(
                order=order_to_production.original_order,
                region=france_region,
                is_jetty=True,
            ),
        )

        ProcessOrderToProduction(order_to_production).calculate_recycle_tax()
        items = order_to_production.original_order.items.aggregate(
            Sum('recycle_tax_value')
        )
        assert (
            items['recycle_tax_value__sum'] == mocked_get_recycle_tax_value.return_value
        )

    @mock.patch(
        'orders.services.process_to_production.get_recycle_tax_value',
        return_value=Decimal('3.08'),
    )
    def test_calculate_recycle_tax_when_order_is_from_france_and_contains_sample_box(
        self,
        mocked_get_recycle_tax_value,
        region_factory,
        order_item_factory,
        product_factory,
        order_to_production_factory,
    ):
        france_region = region_factory(france=True)
        order_to_production = order_to_production_factory(
            original_order__region=france_region,
            original_order__items=[],
        )
        product_factory(
            order=order_to_production.original_order,
            order_item=order_item_factory(
                order=order_to_production.original_order,
                region=france_region,
                is_jetty=True,
            ),
        )
        sample_box = order_item_factory(
            order=order_to_production.original_order,
            region=france_region,
            is_sample_box=True,
        )

        ProcessOrderToProduction(order_to_production).calculate_recycle_tax()
        items = order_to_production.original_order.items.aggregate(
            Sum('recycle_tax_value')
        )
        assert sample_box.recycle_tax_value == Decimal('0')
        assert (
            items['recycle_tax_value__sum'] == mocked_get_recycle_tax_value.return_value
        )

    # TODO: Uncomment after releasing EDD for new orders
    # @mock.patch(
    #     'orders.services.process_to_production'
    #     '.MoveOrderToProduction.move_order_items_to_production'
    # )
    # @mock.patch('orders.internal_api.events.OrderPushedToProductionEvent.execute')
    # @mock.patch('orders.internal_api.events.OrderStatusChangedEvent.execute')
    # @pytest.mark.parametrize(
    #     ('country', 'expected_result'),
    #     (
    #         ('netherlands', True),
    #         ('NETHERLANDS', True),
    #         ('germany', False),
    #         ('', False),
    #         (None, False),
    #     ),
    # )
    # def test_is_estimated_delivery_date_set_according_to_country(
    #     self,
    #     mocked_order_status_changed_event,
    #     mocked_pushed_to_production_event,
    #     _,
    #     order_factory,
    #     order_to_production_factory,
    #     country,
    #     expected_result,
    # ):
    #     order = order_factory(
    #         country=country, paid_at=timezone.now() - timedelta(hours=10)
    #     )
    #     order_to_production = order_to_production_factory(original_order=order)
    #     process_to_production = ProcessOrderToProduction(order_to_production)
    #     process_to_production.process()
    #     assert order.is_estimated_delivery_date is expected_result
