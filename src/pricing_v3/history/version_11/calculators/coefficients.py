from decimal import Decimal

from .jetty.coefficients import get_coefficients as jetty_get_coefficients
from .t03.coefficients import get_coefficients as t03_get_coefficients
from .t13.coefficients import get_coefficients as t13_get_coefficients
from .t13_veneer.coefficients import get_coefficients as t13_veneer_get_coefficients


def get_coefficients(region_name: str) -> dict[str, Decimal]:
    return (
        jetty_get_coefficients(region_name)
        | t13_get_coefficients(region_name)
        | t13_veneer_get_coefficients(region_name)
        | t03_get_coefficients(region_name)
    )
