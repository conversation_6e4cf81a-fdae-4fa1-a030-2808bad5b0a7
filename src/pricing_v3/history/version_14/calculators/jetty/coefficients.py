from decimal import Decimal
from typing import Optional

base_coefs = {
    'base': Decima<PERSON>('73.17890333560'),
    'logistic_front_area': <PERSON><PERSON><PERSON>('131.03446552230'),
    'logistic_drawers': Decimal('24.55328836945'),
    'logistic_base': Decimal('49.83697595330'),
}

elements_coefs = {
    'horizontal_length': Decima<PERSON>('42.1277105'),
    'horizontal_unit': <PERSON><PERSON><PERSON>('36.2353815'),
    'vertical_length': Decimal('40.5219990'),
    'vertical_unit': Decimal('16.3103315'),
    'support_length': Decimal('15.9445'),
    'support_unit': Decima<PERSON>('11.8780950'),
    'backs_perimeter': Decimal('12.36870132205'),
    'backs_unit': Decima<PERSON>('3.67375672420'),
    'doors_perimeter': Decimal('13.45778911230'),
    'doors_unit': Decimal('50.36443623655'),
    'drawers_perimeter': Decimal('38.00699500960'),
    'drawers_unit': Decima<PERSON>('110.90280304525'),
    'long_leg_unit': Decimal('21.2649455'),
    'plinth_length': Decimal('53.6315000'),
    'insert_length': Decimal('23.1344660'),
    'insert_unit': Decimal('8.6249710'),
    'cable_management_unit': Decimal('21.4159165'),
    'desk_beam_length': Decimal('107.85258'),
    'desk_beam_unit': Decimal('40.28535'),
}

traits_coefs = {
    't02_factor': Decimal('-0.2961'),
    't01v_factor': Decimal('0.28415'),
    # raptor_factor works differently than other factors.
    # it describes a fake price increase on the raptor-specific fields.
    'raptor_factor': Decimal('2.0'),
    'depth_factor': Decimal('0.08541352'),
    'depth_factor_500': Decimal('0.23287721'),
    'category_bookcase_factor': Decimal('0.192464'),
    'category_chest_factor': Decimal('0.192464'),
    'category_shoerack_factor': Decimal('0.20393'),
    'category_sideboard_factor': Decimal('0.20393'),
    'category_tvstand_factor': Decimal('0.20393'),
    'category_vinyl_storage_factor': Decimal('0.201211'),
    'category_wallstorage_factor': Decimal('0.192464'),
    'category_wardrobe_factor': Decimal('0'),
    'category_bedside_table_factor': Decimal('0.20393'),
    'category_desk_factor': Decimal('0.23272'),
}

margin_coefs = {
    'jetty_margin_base': Decimal('1.39375'),
    # increase margins
    'minimal_front_area_for_increase': Decimal('0.36795'),
    'maximal_front_area_for_increase': Decimal('4.0140'),
    'maximal_increase': Decimal('0.50175'),
}

material_coefs = {
    't01_group_1': Decimal('0.0174'),
    't01_group_2': Decimal('0.0584'),
    't02_group_1': Decimal('0.0298'),
    't02_group_2': Decimal('0.0537'),
    't02_group_3': Decimal('0.0971'),
    't02_group_4': Decimal('0.3425'),
}

vbp_coefs = {
    'width_300': Decimal('0.2230'),
    'height_250': Decimal('0.1115'),
    'depth_400': Decimal('0.05575'),
    'depth_500': Decimal('0.10363581'),
}


additional_factors = {
    'type_01_additional_increase': Decimal('0.133'),
    'type_02_additional_increase': Decimal('0.0815'),
    'type_01v_additional_increase': Decimal('0.133'),
}

RAPTOR_FACTORABLE_COEFFICIENTS = {
    'long_leg_unit',
    'plinth_length',
    'insert_length',
    'insert_unit',
    'cable_management_unit',
    'raptor_factor',
}


def get_coefficients(
    region_name: Optional[str],
    overrides: dict = None,
) -> dict[str, Decimal]:
    defaults = get_default_coefficients(region_name)
    coefficients = defaults['coefficients']

    if overrides:
        coefficients.update(overrides)

    return coefficients


def get_default_coefficients(region_name: Optional[str] = None) -> dict:
    coefficients = {
        **base_coefs,
        **elements_coefs,
        **traits_coefs,
        **margin_coefs,
        **material_coefs,
        **vbp_coefs,
        **additional_factors,
    }
    return {
        'coefficients': coefficients,
    }
