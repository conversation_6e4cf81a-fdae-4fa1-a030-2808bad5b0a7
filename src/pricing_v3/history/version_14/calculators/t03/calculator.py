import typing

from decimal import Decimal

from django.conf import settings

from ..constants import FACTOR_EURO
from .base_prices import (
    calculate_base_price,
    calculate_regional_price_increase,
)
from .coefficients import get_coefficients
from .elements import calculate_elements_price
from .margins import calculate_margins
from .traits import calculate_traits_price

if typing.TYPE_CHECKING:
    from gallery.models import Watty


def prepare_price(watty: 'Watty', region_name: str, pricing_version=None) -> dict:
    if pricing_version is None:
        price_coefficients = get_coefficients(region_name)
    else:
        price_coefficients = pricing_version.coefficients
    base_watty_price = calculate_base_price(watty, price_coefficients)
    watty_price = sum(base_watty_price.values())

    elements_price = calculate_elements_price(watty, price_coefficients)
    watty_price += sum(elements_price.values())

    traits_price = calculate_traits_price(
        watty=watty,
        price_sum=watty_price,
        price_coefficients=price_coefficients,
    )
    watty_price += sum(traits_price.values())

    margins_price = calculate_margins(
        watty=watty,
        price_sum=watty_price,
        sum_without_light=watty_price - elements_price['lighting'],
        price_coefficients=price_coefficients,
    )
    watty_price += sum(margins_price.values())

    regional_increase = calculate_regional_price_increase(
        price=watty_price,
        price_coefficients=price_coefficients,
    )

    watty_price += sum(regional_increase.values())

    return {
        'watty_price_gross': watty_price * Decimal(settings.POLISH_VAT_FACTOR),
        **base_watty_price,
        **elements_price,
        **traits_price,
        **margins_price,
    }


def get_pricing_dict(watty: 'Watty', region_name: str, in_pln: bool) -> dict:
    pricing_dict = prepare_price(watty=watty, region_name=region_name)

    price_in_pln = pricing_dict['watty_price_gross']
    if in_pln:
        final_price = price_in_pln
    else:
        final_price = int(price_in_pln / FACTOR_EURO)
    pricing_dict['total_rounded_gross_regional'] = final_price
    return pricing_dict


def calculate_price(watty: 'Watty', region_name: str, pricing_version=None) -> int:
    pricing_dict = prepare_price(watty, region_name, pricing_version)
    euro_price_gross = pricing_dict['watty_price_gross'] / FACTOR_EURO
    return int(euro_price_gross)
