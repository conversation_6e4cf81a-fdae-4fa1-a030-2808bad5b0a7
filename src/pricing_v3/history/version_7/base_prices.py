import typing

from decimal import Decimal

if typing.TYPE_CHECKING:
    from gallery.models import (
        Jetty,
        Watty,
    )


def get_jetty_front_area(jetty: 'Jetty') -> Decimal:
    width_m = Decimal(jetty.width) / 1000
    height_m = Decimal(jetty.height) / 1000
    return height_m * width_m


def calculate_base_jetty_price(
    jetty: 'Jetty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    """COGS for less customer-valued but important elements, like legs, joints etc."""
    front_area = get_jetty_front_area(jetty)
    return {
        'base': front_area * price_coefficients['base'],
    }


def calculate_jetty_logistics_price(
    jetty: 'Jetty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    front_area = get_jetty_front_area(jetty)
    logistic_cost = (
        front_area * price_coefficients['logistic_front_area']
        + len(jetty.drawers) * price_coefficients['logistic_drawers']
        + price_coefficients['logistic_base']
    )
    return {'logistic': logistic_cost}


def calculate_base_watty_price(
    watty: 'Watty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    """COGS for less customer-valued but important elements, like frame."""
    width_m = Decimal(watty.width) / 1000
    cost_base = (
        price_coefficients['watty_base_width'] * width_m
        + price_coefficients['watty_base_unit']
    )
    return {'base': cost_base}


def calculate_watty_logistics_price(
    watty: 'Watty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {'logistic': Decimal('0')}


def calculate_watty_regional_price_increase(
    price: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    regional_increase = price_coefficients['type_03_additional_increase'] * price
    regional_increase += price_coefficients['regional_increase']
    return {'regional_increase': regional_increase}


def calculate_base_t13_price(
    watty: 'Watty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    """COGS for less customer-valued but important elements, like frame."""
    width_m = Decimal(watty.width) / 1000
    return {
        'base': price_coefficients['type13_base_unit']
        + (width_m * price_coefficients['type13_base_area'])
    }


def calculate_t13_logistics_price(
    watty: 'Watty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    width_m = Decimal(watty.width) / 1000
    height_m = Decimal(watty.height) / 1000
    depth_m = Decimal(watty.depth) / 1000
    volume = width_m * height_m * depth_m
    drawers_count = len(watty.drawers)
    return {
        'logistic': (
            volume * price_coefficients['type13_logs_vol']
            + (drawers_count * price_coefficients['type13_logs_drawers'])
            + price_coefficients['type13_logs_base']
        )
    }


def calculate_t13_regional_price_increase(
    price: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {'regional_increase': 0}
