from decimal import Decimal

import pytest

from custom.enums import ShelfType
from pricing_v3.calculators.jetty import value_based
from pricing_v3.tests.conftest import approx


@pytest.mark.parametrize(
    ('shelf_type', 'width', 'price_before', 'price_after'),
    [
        (ShelfType.TYPE01, 2900, <PERSON><PERSON><PERSON>('100'), <PERSON><PERSON>l('0')),
        (ShelfType.TYPE01, 3000, <PERSON><PERSON><PERSON>('100'), <PERSON><PERSON><PERSON>('7.433')),
        (ShelfType.TYPE01, 3200, Decimal('100'), Decimal('22.30')),
        (ShelfType.TYPE02, 2900, Decimal('100'), Decimal('0')),
        (ShelfType.TYPE02, 3000, Decimal('100'), Decimal('7.433')),
        (ShelfType.TYPE02, 3200, Decimal('100'), Decimal('22.3')),
        (ShelfType.VENEER_TYPE01, 2900, <PERSON>imal('100'), Decimal('0')),
        (ShelfType.VENEER_TYPE01, 3000, <PERSON><PERSON><PERSON>('100'), Decimal('0')),
        (ShelfType.VENEER_TYPE01, 3200, Decimal('100'), Decimal('0')),
    ],
)
def test_vbp_width_over_300(
    shelf_type,
    width,
    price_before,
    price_after,
    price_coefficients,
):
    width_increase = value_based._calculate_width_value_increase(
        price_before,
        shelf_type,
        width,
        price_coefficients,
    )
    assert approx(width_increase) == price_after


@pytest.mark.parametrize(
    ('shelf_type', 'height', 'price_before', 'price_after'),
    [
        (ShelfType.TYPE01, 2000, Decimal('100'), Decimal('0')),
        (ShelfType.TYPE01, 3000, Decimal('100'), Decimal('11.15')),
        (ShelfType.TYPE02, 2000, Decimal('100'), Decimal('0')),
        (ShelfType.TYPE02, 3000, Decimal('100'), Decimal('11.15')),
        (ShelfType.VENEER_TYPE01, 2000, Decimal('100'), Decimal('0')),
        (ShelfType.VENEER_TYPE01, 3000, Decimal('100'), Decimal('0')),
    ],
)
def test_vbp_height_over_250(
    shelf_type,
    height,
    price_before,
    price_after,
    price_coefficients,
):
    height_increase = value_based._calculate_height_value_increase(
        price_before,
        shelf_type,
        height,
        price_coefficients,
    )
    assert height_increase == price_after


@pytest.mark.parametrize(
    ('shelf_type', 'depth', 'price_before', 'price_after'),
    [
        (ShelfType.TYPE01, 320, Decimal('100'), Decimal('0')),
        (ShelfType.TYPE01, 400, Decimal('100'), Decimal('5.575')),
        (ShelfType.TYPE01, 500, Decimal('100'), Decimal('10.363581')),
        (ShelfType.TYPE01, 600, Decimal('100'), Decimal('25.81')),
        (ShelfType.TYPE02, 320, Decimal('100'), Decimal('0')),
        (ShelfType.TYPE02, 400, Decimal('100'), Decimal('5.575')),
        (ShelfType.TYPE02, 500, Decimal('100'), Decimal('10.363581')),
        (ShelfType.TYPE02, 600, Decimal('100'), Decimal('25.81')),
        (ShelfType.VENEER_TYPE01, 320, Decimal('100'), Decimal('0')),
        (ShelfType.VENEER_TYPE01, 400, Decimal('100'), Decimal('0')),
        (ShelfType.VENEER_TYPE01, 500, Decimal('100'), Decimal('0')),
        (ShelfType.VENEER_TYPE01, 600, Decimal('100'), Decimal('0')),
    ],
)
def test_vbp_depth_400(
    shelf_type,
    depth,
    price_before,
    price_after,
    price_coefficients,
):
    depth_increase = value_based._calculate_depth_value_increase(
        price_before,
        shelf_type,
        depth,
        price_coefficients,
    )
    assert depth_increase == price_after
