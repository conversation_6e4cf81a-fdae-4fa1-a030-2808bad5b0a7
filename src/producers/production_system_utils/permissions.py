import logging

from rest_framework import permissions
from rest_framework.exceptions import ValidationError
from rest_framework.request import Request

from producers.production_system_utils.enums import FileType

logger = logging.getLogger('cstm')


class HasProductionSystemPermission(permissions.BasePermission):
    message = 'You are not allowed to perform this action.'

    def has_object_permission(self, request: Request, view, obj):
        request_id = request.headers.get('CSTM-Request-ID')
        file_type = view.kwargs.get('file_type')
        try:
            obj.details.validate_callback_request(
                field_name=FileType(file_type).field_name,
                request_id=request_id,
            )
        except ValidationError as callback_validation_error:
            logger.warning(
                'Callback request validation failed with url %s: %s',
                request.build_absolute_uri(),
                callback_validation_error,
            )
            return False
        return True
