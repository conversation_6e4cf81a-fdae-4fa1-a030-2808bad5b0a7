# Generated by Django 3.2.16 on 2022-12-12 16:44

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('promotions', '0011_promotionconfig_sale_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='promotionconfig',
            name='cart_ribbon_copy_es_header_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='cart_ribbon_copy_es_header_mobile_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='cart_ribbon_copy_nl_header_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='cart_ribbon_copy_nl_header_mobile_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='grid_copy_es_slot_1_header_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='grid_copy_es_slot_1_paragraph_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='grid_copy_nl_slot_1_header_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='grid_copy_nl_slot_1_paragraph_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='grid_es_url',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='grid_nl_url',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='hp_promo_copy_es_cta',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='hp_promo_copy_es_header',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='hp_promo_copy_es_link',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='hp_promo_copy_es_text',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='hp_promo_copy_nl_cta',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='hp_promo_copy_nl_header',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='hp_promo_copy_nl_link',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='hp_promo_copy_nl_text',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_es_button_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_es_header_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_es_header_2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_es_header_3',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_es_link',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_nl_button_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_nl_header_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_nl_header_2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_nl_header_3',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='popup_copy_nl_link',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_es_header_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_es_header_2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_es_header_mobile_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_es_header_mobile_2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_es_link',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_nl_header_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_nl_header_2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_nl_header_mobile_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_nl_header_mobile_2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='ribbon_copy_nl_link',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_es_promo_alert_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_es_promo_alert_3',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_es_promo_alert_button_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_es_promo_alert_mobile_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_es_promo_alert_mobile_1_popup_paragraph_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_es_promo_alert_mobile_1_popup_paragraph_2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_es_promo_alert_mobile_1_popup_paragraph_3',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_nl_promo_alert_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_nl_promo_alert_3',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_nl_promo_alert_button_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_nl_promo_alert_mobile_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_nl_promo_alert_mobile_1_popup_paragraph_1',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_nl_promo_alert_mobile_1_popup_paragraph_2',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='promotionconfig',
            name='configurator_copy_nl_promo_alert_mobile_1_popup_paragraph_3',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='promotionconfig',
            name='languages',
            field=models.IntegerField(
                choices=[
                    (0, 'all'),
                    (1, 'English'),
                    (3, 'French'),
                    (2, 'German'),
                    (4, 'Spanish'),
                    (5, 'Dutch'),
                ],
                default=0,
            ),
        ),
    ]
