from typing import TYPE_CHECKING

from django.conf import settings
from django.db.models import QuerySet

from custom.enums import (
    Furniture,
    ShelfType,
)
from custom.utils.in_memory_cache import expiring_lru_cache
from gallery.enums import FurnitureCategory
from promotions.utils import get_active_promotion
from regions.models import Region

if TYPE_CHECKING:
    from vouchers.models import ItemDiscount


def _process_explicit_category_discounts(
    discounts_with_category: QuerySet['ItemDiscount'],
    category_discounts: dict[FurnitureCategory, float],
    excluded_categories: set[FurnitureCategory],
) -> None:
    for discount in discounts_with_category:
        category = FurnitureCategory(discount.furniture_category)
        if discount.value == 0:
            excluded_categories.add(category)
        else:
            category_discounts[category] = discount.value


def _process_shelf_and_furniture_type_discounts(
    discounts_without_category: QuerySet['ItemDiscount'],
    category_discounts: dict[FurnitureCategory, float],
    excluded_categories: set[FurnitureCategory],
) -> None:
    for discount in discounts_without_category:

        is_sofa_discount = (
            discount.shelf_type == ShelfType.SOFA_TYPE01
            or discount.furniture_type == Furniture.sotty.value
        )
        if not is_sofa_discount:
            continue
        affected_categories = FurnitureCategory.get_sofas()

        for category in affected_categories:
            if discount.value == 0:
                excluded_categories.add(category)
            else:
                category_discounts[category] = discount.value


def _get_categories_affected_by_discount(
    discount: 'ItemDiscount',
) -> set[FurnitureCategory]:
    affected_categories = set()

    is_sofa_discount = (
        discount.shelf_type == ShelfType.SOFA_TYPE01
        or discount.furniture_type == Furniture.sotty.value
    )

    if is_sofa_discount:
        affected_categories.update(FurnitureCategory.get_sofas())

    return affected_categories


def _build_category_result_list(
    categories: list[FurnitureCategory], discount_value: float
) -> list:
    return [
        {
            'categoryName': category,
            'value': f'-{int(discount_value)}%',
        }
        for category in categories
    ]


def _build_specific_discounts_result(
    category_discounts: dict[FurnitureCategory, float],
    excluded_categories: set[FurnitureCategory],
    base_discount_value: float,
) -> list[dict[str, str | FurnitureCategory]]:
    result_categories = []

    for category in FurnitureCategory:
        if category in excluded_categories:
            continue

        discount_value = category_discounts.get(category, base_discount_value)
        result_categories.append(
            {
                'categoryName': category,
                'value': f'-{int(discount_value)}%',
            }
        )

    return result_categories


@expiring_lru_cache(ttl=settings.REGIONS_CACHE_TTL_SECONDS)
def get_categories_in_promo(
    region: Region | None = None,
) -> list[dict[str, str | FurnitureCategory]]:
    promo = get_active_promotion(region=region)
    if not promo or not promo.configs.last().grid_show_category_promotion:
        return []

    all_discounts = promo.promo_code.discounts.all()
    category_discounts = {}
    excluded_categories = set()

    discounts_with_category = all_discounts.exclude(furniture_category='')
    discounts_without_category = all_discounts.filter(furniture_category='')

    _process_explicit_category_discounts(
        discounts_with_category,
        category_discounts,
        excluded_categories,
    )
    _process_shelf_and_furniture_type_discounts(
        discounts_without_category,
        category_discounts,
        excluded_categories,
    )

    has_specific_discounts = category_discounts or excluded_categories

    if has_specific_discounts:
        return _build_specific_discounts_result(
            category_discounts,
            excluded_categories,
            promo.promo_code.value,
        )

    categories = (
        FurnitureCategory.get_shelving()
        if promo.promo_code.excludes_sofas
        else FurnitureCategory
    )
    return _build_category_result_list(categories, promo.promo_code.value)
