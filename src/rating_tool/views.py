import logging

from rest_framework import mixins
from rest_framework.authentication import TokenAuthentication
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from rating_tool.models import BoardCategory
from rating_tool.serializers import (
    BoardCategoryChangesSerializer,
    BoardCategoryDetailSerializer,
    BoardCategorySerializer,
)

logger = logging.getLogger('cstm')


class LocalPagination(PageNumberPagination):
    page_size = 25
    page_size_query_param = 'page_size'
    max_page_size = 500


class BoardCategoryViewSet(
    mixins.CreateModelMixin,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    GenericViewSet,
):
    authentication_classes = (TokenAuthentication,)
    queryset = BoardCategory.objects.all()
    serializer_class = BoardCategorySerializer

    def get_serializer_class(self):
        # for detail use seriailizer with addtional info. Simpler for list (as default)
        if self.action == 'retrieve':
            return BoardCategoryDetailSerializer
        elif self.action == 'create':
            return BoardCategoryChangesSerializer
        return self.serializer_class

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid()
        if 'save' in request.data and request.data['save']:
            serializer.save(user=request.user)
        else:
            serializer.validate_boards(
                serializer.validated_data['changes'],
                serializer.validated_data['parent'],
            )
        return Response(serializer.data)
