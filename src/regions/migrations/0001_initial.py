# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.db.models.deletion
import django.utils.timezone

from django.db import (
    migrations,
    models,
)

import custom.utils.mixins
import regions.mixins


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='Country',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.Char<PERSON>ield(db_index=True, max_length=64)),
                ('code', models.Char<PERSON>ield(db_index=True, max_length=4)),
                ('vat', models.DecimalField(decimal_places=2, max_digits=4)),
                ('language_code', models.Char<PERSON>ield(max_length=4)),
                ('locale', models.Char<PERSON>ield(max_length=8)),
                ('location', models.Char<PERSON>ield(max_length=16)),
                ('region_vat', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name_plural': 'Countries',
            },
            bases=(custom.utils.mixins.ModelStrSummaryMixin, models.Model),
        ),
        migrations.CreateModel(
            name='Currency',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.Char<PERSON>ield(db_index=True, max_length=32, unique=True)),
                ('code', models.CharField(max_length=4, unique=True)),
                ('symbol', models.CharField(max_length=4, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Currencies',
            },
            bases=(
                regions.mixins.RatedInTimeMixin,
                custom.utils.mixins.ModelStrSummaryMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name='CurrencyRate',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('rate', models.DecimalField(decimal_places=2, max_digits=8)),
                (
                    'time',
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                (
                    'currency',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='rates',
                        to='regions.Currency',
                    ),
                ),
            ],
            options={
                'ordering': ['currency', '-time'],
            },
            bases=(
                regions.mixins.RateCopyOnSaveMixin,
                custom.utils.mixins.ModelStrSummaryMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name='GeoCity',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('city_name', models.CharField(db_index=True, max_length=100)),
                ('population', models.IntegerField()),
                (
                    'exported_to_big_query',
                    models.DateTimeField(blank=True, default=None, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name='GeoRegion',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('region_name', models.CharField(db_index=True, max_length=100)),
                ('population', models.IntegerField()),
                (
                    'exported_to_big_query',
                    models.DateTimeField(blank=True, default=None, null=True),
                ),
                (
                    'country',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='regions.Country',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(db_index=True, max_length=32, unique=True)),
                (
                    'default_for_language',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('bg', 'BG'),
                            ('cs', 'CS'),
                            ('da', 'DA'),
                            ('de', 'DE'),
                            ('el', 'EL'),
                            ('en', 'EN'),
                            ('es', 'ES'),
                            ('et', 'ET'),
                            ('fi', 'FI'),
                            ('fr', 'FR'),
                            ('ga', 'GA'),
                            ('hr', 'HR'),
                            ('hu', 'HU'),
                            ('it', 'IT'),
                            ('lt', 'LT'),
                            ('lv', 'LV'),
                            ('nb', 'NB'),
                            ('nl', 'NL'),
                            ('pl', 'PL'),
                            ('pt', 'PT'),
                            ('ro', 'RO'),
                            ('sk', 'SK'),
                            ('sl', 'SL'),
                            ('sv', 'SV'),
                        ],
                        default=None,
                        max_length=4,
                        null=True,
                        unique=True,
                    ),
                ),
                ('is_eu', models.BooleanField(default=False)),
                ('flag', models.CharField(max_length=64, null=True)),
                (
                    'currency',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='regions.Currency',
                    ),
                ),
            ],
            bases=(
                regions.mixins.RatedInTimeMixin,
                custom.utils.mixins.ModelStrSummaryMixin,
                models.Model,
            ),
        ),
        migrations.CreateModel(
            name='RegionRate',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('rate', models.DecimalField(decimal_places=2, max_digits=8)),
                (
                    'time',
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                (
                    'region',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='rates',
                        to='regions.Region',
                    ),
                ),
            ],
            options={
                'ordering': ['region', '-time'],
            },
            bases=(
                regions.mixins.RateCopyOnSaveMixin,
                custom.utils.mixins.ModelStrSummaryMixin,
                models.Model,
            ),
        ),
        migrations.AddField(
            model_name='geocity',
            name='region',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to='regions.GeoRegion'
            ),
        ),
        migrations.AddField(
            model_name='country',
            name='region',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='countries',
                to='regions.Region',
            ),
        ),
        migrations.AlterUniqueTogether(
            name='georegion',
            unique_together=set([('region_name', 'country')]),
        ),
        migrations.AlterUniqueTogether(
            name='geocity',
            unique_together=set([('city_name', 'region')]),
        ),
        migrations.AlterUniqueTogether(
            name='country',
            unique_together=set([('name', 'code', 'region')]),
        ),
    ]
