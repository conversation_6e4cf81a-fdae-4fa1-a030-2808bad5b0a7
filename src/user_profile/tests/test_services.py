import pytest

from events.choices import BrazeSubscriptionGroupStatus
from user_profile.choices import SubscriptionSources
from user_profile.services.subscription_logic import is_subscription_override_forbidden


@pytest.mark.parametrize(
    ('subscription_status', 'subscription_source', 'expected_result'),
    [
        (BrazeSubscriptionGroupStatus.SUBSCRIBED, SubscriptionSources.CONTACT, True),
        (BrazeSubscriptionGroupStatus.SUBSCRIBED, 'contact', True),
        (BrazeSubscriptionGroupStatus.SUBSCRIBED, None, True),
        (
            BrazeSubscriptionGroupStatus.SUBSCRIBED,
            SubscriptionSources.LP_SOFA_TEASER,
            False,
        ),
        (BrazeSubscriptionGroupStatus.UNKNOWN, SubscriptionSources.CONTACT, False),
    ],
)
def test_is_subscription_override_forbidden(
    subscription_status, subscription_source, expected_result
):
    result = is_subscription_override_forbidden(
        status=subscription_status, source=subscription_source
    )

    assert result == expected_result
