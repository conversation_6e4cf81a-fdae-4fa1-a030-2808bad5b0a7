# Generated by Django 3.2.16 on 2022-12-13 12:01

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('vouchers', '0019_remove_itemdiscount_item_conditionals'),
    ]

    operations = [
        migrations.AlterField(
            model_name='itemdiscount',
            name='material',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (
                        0,
                        'T01 White / T02 White / T03 White / T01 Veneer Ash / T13 '
                        'White',
                    ),
                    (
                        1,
                        'T01 Black / T02 Terracotta / T03 Beige / T01 Veneer Oak / T13 '
                        'Sand',
                    ),
                    (3, 'T01 Grey / T02 Sand / T03 Beige Pink / T13 Gray'),
                    (
                        6,
                        'T01 Red / T02 Matte Black / T03 Graphite Pink / T13 Gray '
                        'Plywood',
                    ),
                    (7, 'T01 Yellow / T02 Sky Blue / T13 Black Plywood'),
                    (8, 'T01 Dusty Pink / T02 Burgundy'),
                    (2, 'T02 Midnight Blue / T03 Graphite / T13 Mustard Yellow'),
                    (9, 'T02 Cotton'),
                    (10, 'T02 Gray'),
                    (11, 'T02 Dark Gray'),
                    (12, 'T02 Mustard Yellow'),
                    (4, 'T03 White Pink / T13 Dark Gray'),
                    (5, 'T13 White Plywood'),
                ],
                null=True,
            ),
        ),
    ]
