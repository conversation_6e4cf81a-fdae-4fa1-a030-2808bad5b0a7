from django.contrib.admin import AdminSite
from django.urls import path

from warehouse.views import SamplesDashboard


class WarehouseAdminPage(AdminSite):
    def get_warehouse_admin_urls(self):
        return [
            path(
                'sample_dashboard/',
                self.admin_view(SamplesDashboard.as_view()),
                name='samples_index',
            ),
        ]


urlpatterns = WarehouseAdminPage().get_warehouse_admin_urls()
